(()=>{var o=Chart.helpers.color;window.onload=function(){[{id:"chart-legend-top",legendPosition:"top",color:"primary"},{id:"chart-legend-right",legendPosition:"right",color:"secondary"},{id:"chart-legend-bottom",legendPosition:"bottom",color:"success"},{id:"chart-legend-left",legendPosition:"left",color:"info"}].forEach((function(a){var r,t=document.getElementById(a.id).getContext("2d"),e=(a.legendPosition,r=a.color,{type:"line",data:{labels:["January","February","March","April","May","June","July"],datasets:[{label:"My First dataset",data:[randomScalingFactor(),randomScalingFactor(),randomScalingFactor(),randomScalingFactor(),randomScalingFactor(),randomScalingFactor(),randomScalingFactor()],backgroundColor:o(window.chartColors[r]).alpha(.5).rgbString(),borderColor:window.chartColors[r],borderWidth:1}]},options:{responsive:!0,maintainAspectRatio:!1,legend:{position:"bottom"},scales:{xAxes:[{display:!0,scaleLabel:{display:!0,labelString:"Month"}}],yAxes:[{display:!0,scaleLabel:{display:!0,labelString:"Value"}}]}}});new Chart(t,e),t.shadowColor="rgba(119, 119, 142, 0.2)",t.shadowBlur=10,t.shadowOffsetX=8,t.shadowOffsetY=8}))}})();