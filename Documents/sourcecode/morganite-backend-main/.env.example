APP_NAME=Laravel
APP_ENV=local
APP_KEY=
APP_DEBUG=true
APP_URL=http://127.0.0.1:8000

COPYRIGHT_COMPANY_NAME="Smart Clouds"
# COPYRIGHT_COMPANY_LINK="https://smartsclouds.com"
POWER_COMPANY_NAME="Codexal"
POWER_COMPANY_LINK="https://codexal.co"

LOG_CHANNEL=stack
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=laravel
DB_USERNAME=root
DB_PASSWORD=

# BROADCAST_DRIVER=log
BROADCAST_DRIVER=pusher
QUEUE_DRIVER=database
CACHE_DRIVER=file
FILESYSTEM_DISK=local
QUEUE_CONNECTION=sync
SESSION_DRIVER=file
SESSION_LIFETIME=120

MEMCACHED_HOST=127.0.0.1

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_HOST=mailhog
MAIL_MAILER=smtp
MAIL_DRIVER=smtp
MAIL_HOST=smtp.zoho.com
MAIL_PORT=465
MAIL_ENCRYPTION=SSL
MAIL_USERNAME="<EMAIL>"
MAIL_PASSWORD="X0TsLf8CkGHT"
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

# PUSHER_APP_ID=nmlg5g4tw123123
# PUSHER_APP_KEY=nmlg5g4tw123123
# PUSHER_APP_SECRET=nmlg5g4tw123123
# PUSHER_HOST='app.codexal.com'
# PUSHER_PORT=6001
# PUSHER_SCHEME=https
# PUSHER_APP_CLUSTER=m1.2ee

PUSHER_APP_ID=
PUSHER_APP_KEY=
PUSHER_APP_SECRET=
PUSHER_HOST=
PUSHER_PORT=443
PUSHER_SCHEME=https
PUSHER_APP_CLUSTER=mt1

VITE_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
VITE_PUSHER_HOST="${PUSHER_HOST}"
VITE_PUSHER_PORT="${PUSHER_PORT}"
VITE_PUSHER_SCHEME="${PUSHER_SCHEME}"
VITE_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"

# Google reCAPTCHA
RECAPTCHA_SITE_KEY=6Le572wrAAAAAPWk_pb81urk6k67m4m0btVoqwN3
RECAPTCHA_SECRET_KEY=6Le572wrAAAAAHr-Vhps4FKGBTwds4E_X93XIBz1
