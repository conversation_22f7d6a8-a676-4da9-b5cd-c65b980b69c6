<x-layout.layout banarText="{{__('index.Contact')}}"  banarLink="{{__('index.keep in touch')}}" :banarImage="asset('assets/web/images/web/bg/demo-restaurant-about-title-bg.jpg')">
    <!-- start page title -->
    
    <!-- end page title -->
    <!-- start section -->
   
    <section class="pt-0 ">
        <div class="container"
            data-anime='{ "el": "childs", "translateY": [-15, 0], "opacity": [0,1], "duration": 800, "delay": 200, "staggervalue": 300, "easing": "easeOutQuad" }'>
            <div class="row">
                <div class="col-12 pe-17 background-position-right-top background-no-repeat md-pe-15px"
                    style="background-image: url({{ asset('assets/web/images/web/bg/demo-restaurant-contact-01.jpg') }})">
                    <div id="map" class="map sm-h-350px">
                        <iframe
                        src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d10769.036413150849!2d35.94983687066034!3d31.894325965715716!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x151b5feec4b3a3fd%3A0x1a17eafbe1c5b045!2sMorganite%20for%20food%20technology!5e1!3m2!1sar!2ssa!4v1728981841919!5m2!1sar!2ssa"
                        width="100%" height="100%" style="border:0;" allowfullscreen="" loading="lazy"
                        referrerpolicy="no-referrer-when-downgrade"></iframe>
                    </div>
                </div>
            </div>
            <div class="row align-items-end justify-content-center">
                <div class="col-xl-7 col-lg-6 align-self-start">
                    <span
                        class="fs-140 lg-fs-100 xs-fs-90 fw-700 text-very-light-gray ls-minus-8px lg-ls-minus-5px xs-ls-minus-4px md-w-100 d-block text-center text-lg-start">{{__('index.Write here')}}</span>
                </div>
                <div
                    class="col-xl-5 col-lg-6 col-md-12 contact-form-style-03 position-relative overlap-section-one-fourth md-mt-0">
                    <div
                        class="bg-very-light-gray p-14 position-relative overflow-hidden mt-50px md-mt-25px sm-mt-15px lg-p-10">
                        <i
                            class="bi bi-chat-text fs-140 text-base-color opacity-1 position-absolute top-minus-35px {{App::getLocale()=='en'||App::getLocale()=='fr'?'right':'left'}}-minus-20px"></i>
                        <h2 class="alt-font text-dark-gray mb-15px">{{__('index.How we can help your food?')}}</h2>

                        {{-- Display success/error messages --}}
                        @if(session('success'))
                            <div class="alert alert-success alert-dismissible fade show" role="alert">
                                {{ session('success') }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                            </div>
                        @endif

                        @if(session('error'))
                            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                                {{ session('error') }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                            </div>
                        @endif

                        <form action="{{route('web.contact-mail')}}" method="post">
                            @csrf
                            <div class="position-relative form-group mb-10px">
                                <span class="form-icon text-medium-gray"><i class="bi bi-emoji-smile"></i></span>
                                <input
                                    class="ps-0 border-radius-0px bg-transparent border-color-transparent-dark-very-light form-control required @error('name') is-invalid @enderror"
                                    type="text" name="name" value="{{ old('name') }}" required placeholder="{{__('index.Your name*')}}">
                                @error('name')
                                    <div class="invalid-feedback d-block">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="position-relative form-group mb-10px">
                                <span class="form-icon medium-gray"><i class="bi bi-envelope"></i></span>
                                <input
                                    class="ps-0 border-radius-0px bg-transparent border-color-transparent-dark-very-light form-control required @error('email') is-invalid @enderror"
                                    type="email" name="email" value="{{ old('email') }}" required placeholder="{{__('index.Your email address*')}}">
                                @error('email')
                                    <div class="invalid-feedback d-block">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="position-relative form-group form-textarea mt-10px mb-0">
                                <textarea class="ps-0 border-radius-0px bg-transparent border-color-transparent-dark-very-light form-control @error('comment') is-invalid @enderror"
                                    name="comment" required placeholder="{{__('index.Your message')}}" rows="3">{{ old('comment') }}</textarea>
                                <span class="form-icon medium-gray"><i class="bi bi-chat-square-dots"></i></span>
                                @error('comment')
                                    <div class="invalid-feedback d-block">{{ $message }}</div>
                                @enderror

                                {{-- reCAPTCHA widget --}}
                                @if(config('services.recaptcha.site_key'))
                                    <div class="mt-20px">
                                        <div class="g-recaptcha" data-sitekey="{{ config('services.recaptcha.site_key') }}"></div>
                                        @error('g-recaptcha-response')
                                            <div class="text-danger mt-2">{{ $message }}</div>
                                        @enderror
                                    </div>
                                @endif

                                <input type="hidden" name="redirect" value="">
                                <button
                                    class="btn btn-dark-gray btn-medium btn-switch-text btn-round-edge btn-box-shadow mt-30px"
                                    type="submit">
                                    <span>
                                        <span class="btn-double-text" data-text="{{__('index.Send a message')}}">{{__('index.Send a message')}}</span>
                                    </span>
                                </button>
                                <div class="form-results mt-20px d-none"></div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <!-- end section -->
   

</x-layout.layout>

{{-- Google reCAPTCHA script --}}
@if(config('services.recaptcha.site_key'))
    <script src="https://www.google.com/recaptcha/api.js" async defer></script>
@endif

<script async defer
    src="https://maps.googleapis.com/maps/api/js?key=AIzaSyCA56KqSJ11nQUw_tXgXyNMiPmQeM7EaSA&callback=initMap"></script>
