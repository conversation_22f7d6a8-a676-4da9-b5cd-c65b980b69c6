<?php

namespace Tests\Feature\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Facades\Mail;
use Tests\TestCase;

class ContactFormTest extends TestCase
{
    use WithFaker;

    /**
     * Test contact form validation without reCAPTCHA
     *
     * @return void
     */
    public function test_contact_form_requires_recaptcha()
    {
        // Set reCAPTCHA configuration for testing
        config(['services.recaptcha.secret_key' => 'test-secret-key']);

        $response = $this->post(route('web.contact-mail'), [
            'name' => '<PERSON>e',
            'email' => '<EMAIL>',
            'comment' => 'This is a test message',
            // Missing g-recaptcha-response
        ]);

        $response->assertSessionHasErrors(['g-recaptcha-response']);
    }

    /**
     * Test contact form validation with required fields missing
     *
     * @return void
     */
    public function test_contact_form_requires_all_fields()
    {
        $response = $this->post(route('web.contact-mail'), []);

        $response->assertSessionHasErrors(['name', 'email', 'comment']);
    }

    /**
     * Test contact form with invalid email
     *
     * @return void
     */
    public function test_contact_form_requires_valid_email()
    {
        $response = $this->post(route('web.contact-mail'), [
            'name' => 'John Doe',
            'email' => 'invalid-email',
            'comment' => 'This is a test message',
        ]);

        $response->assertSessionHasErrors(['email']);
    }

    /**
     * Test contact form works when reCAPTCHA is not configured
     *
     * @return void
     */
    public function test_contact_form_works_without_recaptcha_config()
    {
        // Clear reCAPTCHA configuration
        config(['services.recaptcha.secret_key' => null]);

        Mail::fake();

        $response = $this->post(route('web.contact-mail'), [
            'name' => 'John Doe',
            'email' => '<EMAIL>',
            'comment' => 'This is a test message',
        ]);

        $response->assertRedirect();
        $response->assertSessionHas('success');
    }
}
