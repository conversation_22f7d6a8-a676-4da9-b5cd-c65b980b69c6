<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Default Filesystem Disk
    |--------------------------------------------------------------------------
    |
    | Here you may specify the default filesystem disk that should be used
    | by the framework. The "local" disk, as well as a variety of cloud
    | based disks are available to your application. Just store away!
    |
    */

    'default' => env('FILESYSTEM_DISK', 'local'),

    /*
    |--------------------------------------------------------------------------
    | Filesystem Disks
    |--------------------------------------------------------------------------
    |
    | Here you may configure as many filesystem "disks" as you wish, and you
    | may even configure multiple disks of the same driver. Defaults have
    | been set up for each driver as an example of the required values.
    |
    | Supported Drivers: "local", "ftp", "sftp", "s3"
    |
    */

    'disks' => [

        'local' => [
            'driver' => 'local',
            'root' => storage_path('app'),
            'throw' => false,
        ],

        'public' => [
            'driver' => 'local',
            'root' => storage_path('app/public'),
            'url' => env('APP_URL') . '/storage',
            'visibility' => 'public',
            'throw' => false,
        ],

        'product' => [
            'driver' => 'local',
            'root' => storage_path('app/product'),
            'url' => env('APP_URL') . '/storage',
            'visibility' => 'product',
            'throw' => false,
        ],

        'appimage' => [
            'driver' => 'local',
            'root' => storage_path('app/appimage'),
            'url' => env('APP_URL') . '/storage',
            'visibility' => 'appimage',
            'throw' => false,
        ],

        'impact' => [
            'driver' => 'local',
            'root' => storage_path('app/impact'),
            'url' => env('APP_URL') . '/storage',
            'visibility' => 'impact',
            'throw' => false,
        ],

        'blog' => [
            'driver' => 'local',
            'root' => storage_path('app/blog'),
            'url' => env('APP_URL') . '/storage',
            'visibility' => 'blog',
            'throw' => false,
        ],

        'team' => [
            'driver' => 'local',
            'root' => storage_path('app/team'),
            'url' => env('APP_URL') . '/storage',
            'visibility' => 'team',
            'throw' => false,
        ],

        'banner' => [
            'driver' => 'local',
            'root' => storage_path('app/banner'),
            'url' => env('APP_URL') . '/storage',
            'visibility' => 'banner',
            'throw' => false,
        ],
        'story' => [
            'driver' => 'local',
            'root' => storage_path('app/story'),
            'url' => env('APP_URL') . '/storage',
            'visibility' => 'story',
            'throw' => false,
        ],
        'materials' => [
            'driver' => 'local',
            'root' => storage_path('app/materials'),
            'url' => env('APP_URL') . '/storage',
            'visibility' => 'materials',
            'throw' => false,
        ],

        'certificate' => [
            'driver' => 'local',
            'root' => storage_path('app/certificate'),
            'url' => env('APP_URL') . '/storage',
            'visibility' => 'certificate',
            'throw' => false,
        ],
        'firebasenotification' => [
            'driver' => 'local',
            'root' => storage_path('app/firebasenotification'),
            'url' => env('APP_URL') . '/storage',
            'visibility' => 'firebasenotification',
            'throw' => false,
        ],

        // 'applang' => [
        //     'driver' => 'applang',
        //     'root' => storage_path('app/applang'),
        //     'url' => env('APP_URL') . '/applang',
        //     'visibility' => 'public',
        // ],

        's3' => [
            'driver' => 's3',
            'key' => env('AWS_ACCESS_KEY_ID'),
            'secret' => env('AWS_SECRET_ACCESS_KEY'),
            'region' => env('AWS_DEFAULT_REGION'),
            'bucket' => env('AWS_BUCKET'),
            'url' => env('AWS_URL'),
            'endpoint' => env('AWS_ENDPOINT'),
            'use_path_style_endpoint' => env('AWS_USE_PATH_STYLE_ENDPOINT', false),
            'throw' => false,
        ],

    ],

    /*
    |--------------------------------------------------------------------------
    | Symbolic Links
    |--------------------------------------------------------------------------
    |
    | Here you may configure the symbolic links that will be created when the
    | `storage:link` Artisan command is executed. The array keys should be
    | the locations of the links and the values should be their targets.
    |
    */

    'links' => [
        public_path('storage') => storage_path('app/public'),
        public_path('product') => storage_path('app/product'),
        public_path('banner') => storage_path('app/banner'),
        public_path('category') => storage_path('app/category'),
        public_path('appimage') => storage_path('app/appimage'),
        public_path('blog') => storage_path('app/blog'),
        public_path('impact') => storage_path('app/impact'),
        public_path('team') => storage_path('app/team'),
        public_path('story') => storage_path('app/story'),
        public_path('materials') => storage_path('app/materials'),
        public_path('certificate') => storage_path('app/certificate'),
        public_path('firebasenotification') => storage_path('app/firebasenotification'),

        // public_path('applang') => storage_path('app/applang'),

    ],

];
