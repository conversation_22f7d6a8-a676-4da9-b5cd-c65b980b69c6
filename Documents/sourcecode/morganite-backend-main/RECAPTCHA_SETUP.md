# reCAPTCHA Integration Setup

This document explains how to set up Google reCAPTCHA for the contact form in the Morganite Group website.

## Overview

The contact form now includes Google reCAPTCHA v2 integration to prevent spam submissions and enhance security.

## Setup Instructions

### 1. Get reCAPTCHA Keys

1. Visit [Google reCAPTCHA Admin Console](https://www.google.com/recaptcha/admin)
2. Click "Create" to add a new site
3. Choose reCAPTCHA v2 (checkbox)
4. Add your domain(s) to the domains list
5. Copy the Site Key and Secret Key

### 2. Configure Environment Variables

Add the following to your `.env` file:

```env
RECAPTCHA_SITE_KEY=your_site_key_here
RECAPTCHA_SECRET_KEY=your_secret_key_here
```

### 3. Test the Integration

1. Visit the contact page: `/contact`
2. Fill out the form
3. Complete the reCAPTCHA challenge
4. Submit the form

## Features

- **Automatic Integration**: reCAPTC<PERSON> only appears when keys are configured
- **Graceful Fallback**: Form works normally if reCAP<PERSON><PERSON> is not configured
- **Server-side Validation**: All validation happens on the server for security
- **Error Handling**: Proper error messages for failed validation
- **Responsive Design**: reCAPTCHA integrates with existing form styling

## Files Modified

- `config/services.php` - Added reCAPTCHA configuration
- `app/Rules/RecaptchaRule.php` - Custom validation rule
- `app/Http/Controllers/Web/WebHomeController.php` - Updated contact form handler
- `resources/views/web/contact/contact.blade.php` - Updated contact form template
- `tests/Feature/Feature/ContactFormTest.php` - Added tests

## Testing

Run the contact form tests:

```bash
php artisan test tests/Feature/Feature/ContactFormTest.php
```

## Troubleshooting

### reCAPTCHA Not Showing
- Check that `RECAPTCHA_SITE_KEY` is set in `.env`
- Verify the domain is registered in Google reCAPTCHA console
- Check browser console for JavaScript errors

### Validation Always Fails
- Verify `RECAPTCHA_SECRET_KEY` is correct in `.env`
- Check server logs for API errors
- Ensure server can make HTTPS requests to Google

### Form Works Without reCAPTCHA
This is expected behavior when reCAPTCHA keys are not configured. The form will work normally without the reCAPTCHA challenge.

## Security Notes

- Never commit reCAPTCHA keys to version control
- Use different keys for development and production
- Monitor reCAPTCHA analytics in Google console
- Consider implementing rate limiting for additional protection
