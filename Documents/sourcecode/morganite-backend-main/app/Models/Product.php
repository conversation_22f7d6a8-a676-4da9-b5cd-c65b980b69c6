<?php

namespace App\Models;

use App\Traits\TranslateTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\App;
// use Laratrust\Traits\LaratrustUserTrait;

class Product extends Model
{
    // use LaratrustUserTrait;
    use HasFactory;
    use SoftDeletes;

    // use Translatable trait to translate the columns
    use TranslateTrait;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    // protected $table;

    /**
     * The relations to eager load on every query.
     *
     * @var array
     */
    // protected $with = [];

    /**
     * The relationship counts that should be eager loaded on every query.
     *
     * @var array
     */
    // protected $withCount = [];

    /**
     * The number of models to return for pagination.
     *
     * @var int
     */
    protected $perPage = 15;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'add_by',
        'slug',


        'sku',
        'the_name',
        'the_short_description',
        'the_long_description',
        'status',
        'thumbnail',

        'show',
        'sort',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'show',
        'sort',
    ];


    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'show' => 'boolean',
        'sort' => 'integer',

        'the_name' => 'json',
        'the_short_description' => 'json',
        'the_long_description' => 'json',
    ];

    public function crud_name()
    {
        return $this->name();
    }

    public function name($lang = null)
    {
        return $this->translateCol($this->the_name, $lang);
    }

    public function short_description($lang = null)
    {
        return $this->translateCol($this->the_short_description, $lang);
    }

    public function long_description($lang = null)
    {
        return $this->translateCol($this->the_long_description, $lang);
    }

    public static function livewireSearch($search)
    {
        if (empty($search)) return static::query();

        return static::query()->where(function ($q) use ($search) {
            $q->whereIn('id', array_map('intval', explode(',', $search)));


            $q->orWhereSearch('sku', "%$search%");
            $q->orWhereSearch('the_name', "%$search%");
            $q->orWhereSearch('the_short_description', "%$search%");
            $q->orWhereSearch('the_long_description', "%$search%");
            $q->orWhereSearch('status', "%$search%");
            $q->orWhereSearch('thumbnail', "%$search%");
        });
    }


    public function categories()
    {
        return $this->belongsToMany(Category::class);
    }

    public function tags()
    {
        return $this->belongsToMany(Tag::class);
    }

    public function attribute_value()
    {
        return $this->hasOne(AttributeValue::class)->latestOfMany();
    }

    public function images()
    {
        return $this->hasMany(Appimage::class, 'the_model_id')->where('the_model_name', 'product');
    }

    public function comments()
    {
        return $this->hasMany(Commint::class, 'the_model_id');
    }

    public function rates()
    {
        return $this->hasMany(Rate::class);
    }

    public function pers($v = null)
    {
        if ($this->rates->count() == 0) return 0;
        if ($v)
            $co = $this->rates->where('value', $v)->count();
        else
            $co = $this->rates->count();

        return round(($co / $this->rates->count()) * 100, 0);
    }

    public function per()
    {
        if ($this->rates->count() == 0) return 0;

        return round($this->rates->sum('value') / $this->rates->count(), 1);
    }
}
