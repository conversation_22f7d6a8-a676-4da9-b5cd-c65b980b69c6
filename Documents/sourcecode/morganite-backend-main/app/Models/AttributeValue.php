<?php

namespace App\Models;

use App\Traits\TranslateTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\App;
// use Laratrust\Traits\LaratrustUserTrait;

class AttributeValue extends Model
{
    // use LaratrustUserTrait;
    use HasFactory;
    use SoftDeletes;

    // use Translatable trait to translate the columns
    use TranslateTrait;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    // protected $table;

    /**
     * The relations to eager load on every query.
     *
     * @var array
     */
    // protected $with = [];

    /**
     * The relationship counts that should be eager loaded on every query.
     *
     * @var array
     */
    // protected $withCount = [];

    /**
     * The number of models to return for pagination.
     *
     * @var int
     */
    protected $perPage = 15;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'add_by',
        'slug',


        'sku',
        'the_name',
        'the_description',
        'price',
        'discount_type',
        'discount_amount',
        'manage_stock',
        'quantity_in_stock',
        'discount_from_date',
        'discount_to_date',
        'status',
        'attribute_group_id',
        'product_id',

        'show',
        'sort',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'show',
        'sort',
    ];


    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'show' => 'boolean',
        'sort' => 'integer',

        'the_name' => 'json',
        'the_description' => 'json'
    ];

    public function crud_name()
    {
        return $this->name();
    }

    public function name($lang = null)
    {
        return $this->translateCol($this->the_name, $lang);
    }

    public function description($lang = null)
    {
        return $this->translateCol($this->the_description, $lang);
    }

    public static function livewireSearch($search)
    {
        if (empty($search)) return static::query();

        return static::query()->where(function ($q) use ($search) {
            $q->whereIn('id', array_map('intval', explode(',', $search)));


            $q->orWhereSearch('sku', "%$search%");
            $q->orWhereSearch('the_name', "%$search%");
            $q->orWhereSearch('the_description', "%$search%");
            $q->orWhereSearch('price', "%$search%");
            $q->orWhereSearch('discount_type', "%$search%");
            $q->orWhereSearch('discount_amount', "%$search%");
            $q->orWhereSearch('manage_stock', "%$search%");
            $q->orWhereSearch('quantity_in_stock', "%$search%");
            $q->orWhereSearch('discount_from_date', "%$search%");
            $q->orWhereSearch('discount_to_date', "%$search%");
            $q->orWhereSearch('status', "%$search%");
            $q->orWhere('attribute_group_id', 'like', "%$search%");
            $q->orWhere('product_id', 'like', "%$search%");
        });
    }



    public function attribute_group()
    {
        return $this->belongsTo(AttributeGroup::class);
    }


    public function product()
    {
        return $this->belongsTo(Product::class);
    }

    public function have_descount()
    {
        if ($this->discount_amount == 0)
            return false;


        return true;
    }

    public function final_price()
    {
        if ($this->have_descount())
            return $this->discount_amount;

        return $this->price;
    }
}
