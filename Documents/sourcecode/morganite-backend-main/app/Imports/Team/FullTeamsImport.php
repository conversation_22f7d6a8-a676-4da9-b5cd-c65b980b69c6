<?php

namespace App\Imports\Team;

use App\Models\Team;
use Maatwebsite\Excel\Concerns\ToCollection;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\WithHeadingRow;

class FullTeamsImport implements ToCollection, WithHeadingRow
{
    /**
     * @param Collection $collection
     */
    public function collection(Collection $rows)
    {
        foreach ($rows as $row) {

            $id = trim($row['id']);
            $slug = trim($row['slug']);
            
            
                        $the_name = trim($row['the_name']);

                        $the_sub_title = trim($row['the_sub_title']);

                        $the_bio = trim($row['the_bio']);

                        $image = trim($row['image']);

                        $phone = trim($row['phone']);

                        $email = trim($row['email']);

                        $fb_link = trim($row['fb_link']);

                        $tw_link = trim($row['tw_link']);

                        $in_link = trim($row['in_link']);

                        $ln_link = trim($row['ln_link']);


            $team = Team::find($id);

            if (!$team) {
                $team = Team::create([
                    'slug' => $slug,

                    
                        'the_name' => $the_name,
                        'the_sub_title' => $the_sub_title,
                        'the_bio' => $the_bio,
                        'image' => $image,
                        'phone' => $phone,
                        'email' => $email,
                        'fb_link' => $fb_link,
                        'tw_link' => $tw_link,
                        'in_link' => $in_link,
                        'ln_link' => $ln_link,
                ]);
            } else {
                $team->update([
                    'slug' => $slug,

                    
                        'the_name' => $the_name,
                        'the_sub_title' => $the_sub_title,
                        'the_bio' => $the_bio,
                        'image' => $image,
                        'phone' => $phone,
                        'email' => $email,
                        'fb_link' => $fb_link,
                        'tw_link' => $tw_link,
                        'in_link' => $in_link,
                        'ln_link' => $ln_link,
                ]);
            }
        }
    }
}
