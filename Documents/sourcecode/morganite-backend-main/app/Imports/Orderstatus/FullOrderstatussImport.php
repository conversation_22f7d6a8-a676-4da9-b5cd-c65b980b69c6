<?php

namespace App\Imports\OrderStatus;

use App\Models\OrderStatus;
use Maatwebsite\Excel\Concerns\ToCollection;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\WithHeadingRow;

class FullOrderStatussImport implements ToCollection, WithHeadingRow
{
    /**
     * @param Collection $collection
     */
    public function collection(Collection $rows)
    {
        foreach ($rows as $row) {

            $id = trim($row['id']);
            $slug = trim($row['slug']);
            
            
                        $the_name = trim($row['the_name']);

                        $bg_color = trim($row['bg_color']);

                        $text_color = trim($row['text_color']);


            $orderstatus = OrderStatus::find($id);

            if (!$orderstatus) {
                $orderstatus = OrderStatus::create([
                    'slug' => $slug,

                    
                        'the_name' => $the_name,
                        'bg_color' => $bg_color,
                        'text_color' => $text_color,
                ]);
            } else {
                $orderstatus->update([
                    'slug' => $slug,

                    
                        'the_name' => $the_name,
                        'bg_color' => $bg_color,
                        'text_color' => $text_color,
                ]);
            }
        }
    }
}
