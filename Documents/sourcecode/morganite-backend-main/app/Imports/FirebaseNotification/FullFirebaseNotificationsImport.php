<?php

namespace App\Imports\FirebaseNotification;

use App\Models\FirebaseNotification;
use Maatwebsite\Excel\Concerns\ToCollection;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\WithHeadingRow;

class FullFirebaseNotificationsImport implements ToCollection, WithHeadingRow
{
    /**
     * @param Collection $collection
     */
    public function collection(Collection $rows)
    {
        foreach ($rows as $row) {

            $id = trim($row['id']);
            $slug = trim($row['slug']);
            
            
                        $user_id = trim($row['user_id']);

                        $notification_text_id = trim($row['notification_text_id']);

                        $the_page = trim($row['the_page']);

                        $title = trim($row['title']);

                        $body = trim($row['body']);

                        $data_id = trim($row['data_id']);

                        $seen = trim($row['seen']);

                        $seen_at = trim($row['seen_at']);


            $firebasenotification = FirebaseNotification::find($id);

            if (!$firebasenotification) {
                $firebasenotification = FirebaseNotification::create([
                    'slug' => $slug,

                    
                        'user_id' => $user_id,
                        'notification_text_id' => $notification_text_id,
                        'the_page' => $the_page,
                        'title' => $title,
                        'body' => $body,
                        'data_id' => $data_id,
                        'seen' => $seen,
                        'seen_at' => $seen_at,
                ]);
            } else {
                $firebasenotification->update([
                    'slug' => $slug,

                    
                        'user_id' => $user_id,
                        'notification_text_id' => $notification_text_id,
                        'the_page' => $the_page,
                        'title' => $title,
                        'body' => $body,
                        'data_id' => $data_id,
                        'seen' => $seen,
                        'seen_at' => $seen_at,
                ]);
            }
        }
    }
}
