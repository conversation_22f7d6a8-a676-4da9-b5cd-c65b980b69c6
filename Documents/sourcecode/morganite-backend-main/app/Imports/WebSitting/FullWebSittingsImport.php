<?php

namespace App\Imports\WebSitting;

use App\Models\WebSitting;
use Maatwebsite\Excel\Concerns\ToCollection;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\WithHeadingRow;

class FullWebSittingsImport implements ToCollection, WithHeadingRow
{
    /**
     * @param Collection $collection
     */
    public function collection(Collection $rows)
    {
        foreach ($rows as $row) {

            $id = trim($row['id']);
            $slug = trim($row['slug']);


            $e_commerce = trim($row['e_commerce']);


            $websitting = WebSitting::find($id);

            if (!$websitting) {
                $websitting = WebSitting::create([
                    'slug' => $slug,


                    'e_commerce' => $e_commerce,
                ]);
            } else {
                $websitting->update([
                    'slug' => $slug,


                    'e_commerce' => $e_commerce,
                ]);
            }
        }
    }
}
