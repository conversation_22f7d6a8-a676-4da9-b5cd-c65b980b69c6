<?php

namespace App\Imports\Currency;

use App\Models\Currency;
use Maatwebsite\Excel\Concerns\ToCollection;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\WithHeadingRow;

class FullCurrencysImport implements ToCollection, WithHeadingRow
{
    /**
     * @param Collection $collection
     */
    public function collection(Collection $rows)
    {
        foreach ($rows as $row) {

            $id = trim($row['id']);
            $slug = trim($row['slug']);
            
            
                        $the_name = trim($row['the_name']);

                        $code = trim($row['code']);

                        $convert_rate = trim($row['convert_rate']);

                        $status = trim($row['status']);


            $currency = Currency::find($id);

            if (!$currency) {
                $currency = Currency::create([
                    'slug' => $slug,

                    
                        'the_name' => $the_name,
                        'code' => $code,
                        'convert_rate' => $convert_rate,
                        'status' => $status,
                ]);
            } else {
                $currency->update([
                    'slug' => $slug,

                    
                        'the_name' => $the_name,
                        'code' => $code,
                        'convert_rate' => $convert_rate,
                        'status' => $status,
                ]);
            }
        }
    }
}
