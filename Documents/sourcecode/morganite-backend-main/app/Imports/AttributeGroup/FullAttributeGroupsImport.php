<?php

namespace App\Imports\AttributeGroup;

use App\Models\AttributeGroup;
use Maatwebsite\Excel\Concerns\ToCollection;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\WithHeadingRow;

class FullAttributeGroupsImport implements ToCollection, WithHeadingRow
{
    /**
     * @param Collection $collection
     */
    public function collection(Collection $rows)
    {
        foreach ($rows as $row) {

            $id = trim($row['id']);
            $slug = trim($row['slug']);
            
            
                        $the_name = trim($row['the_name']);

                        $type = trim($row['type']);

                        $key = trim($row['key']);


            $attributegroup = AttributeGroup::find($id);

            if (!$attributegroup) {
                $attributegroup = AttributeGroup::create([
                    'slug' => $slug,

                    
                        'the_name' => $the_name,
                        'type' => $type,
                        'key' => $key,
                ]);
            } else {
                $attributegroup->update([
                    'slug' => $slug,

                    
                        'the_name' => $the_name,
                        'type' => $type,
                        'key' => $key,
                ]);
            }
        }
    }
}
