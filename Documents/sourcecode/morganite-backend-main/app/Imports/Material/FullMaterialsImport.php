<?php

namespace App\Imports\Material;

use App\Models\Material;
use Maatwebsite\Excel\Concerns\ToCollection;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\WithHeadingRow;

class FullMaterialsImport implements ToCollection, WithHeadingRow
{
    /**
     * @param Collection $collection
     */
    public function collection(Collection $rows)
    {
        foreach ($rows as $row) {

            $id = trim($row['id']);
            $slug = trim($row['slug']);


            $the_title = trim($row['the_title']);

            $the_description = trim($row['the_description']);


            $material = Material::find($id);

            if (!$material) {
                $material = Material::create([
                    'slug' => $slug,


                    'the_title' => $the_title,
                    'the_description' => $the_description,
                ]);
            } else {
                $material->update([
                    'slug' => $slug,


                    'the_title' => $the_title,
                    'the_description' => $the_description,
                ]);
            }
        }
    }
}
