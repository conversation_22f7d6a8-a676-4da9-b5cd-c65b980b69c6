<?php

namespace App\Imports\Applang;

use App\Models\Applang;
use Maatwebsite\Excel\Concerns\ToCollection;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\WithHeadingRow;

class FullApplangsImport implements ToCollection, WithHeadingRow
{
    /**
     * @param Collection $collection
     */
    public function collection(Collection $rows)
    {
        foreach ($rows as $row) {

            $id = trim($row['id']);
            $slug = trim($row['slug']);
            
            
                        $full_name = trim($row['full_name']);

                        $short_name = trim($row['short_name']);

                        $icon = trim($row['icon']);

                        $status = trim($row['status']);


            $applang = Applang::find($id);

            if (!$applang) {
                $applang = Applang::create([
                    'slug' => $slug,

                    
                        'full_name' => $full_name,
                        'short_name' => $short_name,
                        'icon' => $icon,
                        'status' => $status,
                ]);
            } else {
                $applang->update([
                    'slug' => $slug,

                    
                        'full_name' => $full_name,
                        'short_name' => $short_name,
                        'icon' => $icon,
                        'status' => $status,
                ]);
            }
        }
    }
}
