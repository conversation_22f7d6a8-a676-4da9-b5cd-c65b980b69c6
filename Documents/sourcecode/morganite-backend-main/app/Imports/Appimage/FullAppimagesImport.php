<?php

namespace App\Imports\Appimage;

use App\Models\Appimage;
use Maatwebsite\Excel\Concerns\ToCollection;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\WithHeadingRow;

class FullAppimagesImport implements ToCollection, WithHeadingRow
{
    /**
     * @param Collection $collection
     */
    public function collection(Collection $rows)
    {
        foreach ($rows as $row) {

            $id = trim($row['id']);
            $slug = trim($row['slug']);
            
            
                        $the_model_name = trim($row['the_model_name']);

                        $the_model_id = trim($row['the_model_id']);

                        $place = trim($row['place']);


            $appimage = Appimage::find($id);

            if (!$appimage) {
                $appimage = Appimage::create([
                    'slug' => $slug,

                    
                        'the_model_name' => $the_model_name,
                        'the_model_id' => $the_model_id,
                        'place' => $place,
                ]);
            } else {
                $appimage->update([
                    'slug' => $slug,

                    
                        'the_model_name' => $the_model_name,
                        'the_model_id' => $the_model_id,
                        'place' => $place,
                ]);
            }
        }
    }
}
