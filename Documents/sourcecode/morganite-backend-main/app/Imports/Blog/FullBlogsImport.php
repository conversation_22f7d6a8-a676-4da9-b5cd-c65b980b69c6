<?php

namespace App\Imports\Blog;

use App\Models\Blog;
use Maatwebsite\Excel\Concerns\ToCollection;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\WithHeadingRow;

class FullBlogsImport implements ToCollection, WithHeadingRow
{
    /**
     * @param Collection $collection
     */
    public function collection(Collection $rows)
    {
        foreach ($rows as $row) {

            $id = trim($row['id']);
            $slug = trim($row['slug']);
            
            
                        $the_title = trim($row['the_title']);

                        $the_content = trim($row['the_content']);

                        $thumbnail = trim($row['thumbnail']);

                        $the_summary = trim($row['the_summary']);


            $blog = Blog::find($id);

            if (!$blog) {
                $blog = Blog::create([
                    'slug' => $slug,

                    
                        'the_title' => $the_title,
                        'the_content' => $the_content,
                        'thumbnail' => $thumbnail,
                        'the_summary' => $the_summary,
                ]);
            } else {
                $blog->update([
                    'slug' => $slug,

                    
                        'the_title' => $the_title,
                        'the_content' => $the_content,
                        'thumbnail' => $thumbnail,
                        'the_summary' => $the_summary,
                ]);
            }
        }
    }
}
