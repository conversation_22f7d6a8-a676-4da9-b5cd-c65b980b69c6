<?php

namespace App\Imports\StaticPage;

use App\Models\StaticPage;
use Maatwebsite\Excel\Concerns\ToCollection;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\WithHeadingRow;

class FullStaticPagesImport implements ToCollection, WithHeadingRow
{
    /**
     * @param Collection $collection
     */
    public function collection(Collection $rows)
    {
        foreach ($rows as $row) {

            $id = trim($row['id']);
            $slug = trim($row['slug']);
            
            
                        $the_title = trim($row['the_title']);

                        $page_link = trim($row['page_link']);

                        $the_content = trim($row['the_content']);


            $staticpage = StaticPage::find($id);

            if (!$staticpage) {
                $staticpage = StaticPage::create([
                    'slug' => $slug,

                    
                        'the_title' => $the_title,
                        'page_link' => $page_link,
                        'the_content' => $the_content,
                ]);
            } else {
                $staticpage->update([
                    'slug' => $slug,

                    
                        'the_title' => $the_title,
                        'page_link' => $page_link,
                        'the_content' => $the_content,
                ]);
            }
        }
    }
}
