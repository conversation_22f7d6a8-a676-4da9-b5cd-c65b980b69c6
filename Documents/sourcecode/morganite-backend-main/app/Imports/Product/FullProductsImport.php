<?php

namespace App\Imports\Product;

use App\Models\Product;
use Maatwebsite\Excel\Concerns\ToCollection;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\WithHeadingRow;

class FullProductsImport implements ToCollection, WithHeadingRow
{
    /**
     * @param Collection $collection
     */
    public function collection(Collection $rows)
    {
        foreach ($rows as $row) {

            $id = trim($row['id']);
            $slug = trim($row['slug']);
            
            
                        $sku = trim($row['sku']);

                        $the_name = trim($row['the_name']);

                        $the_short_description = trim($row['the_short_description']);

                        $the_long_description = trim($row['the_long_description']);

                        $status = trim($row['status']);

                        $thumbnail = trim($row['thumbnail']);


            $product = Product::find($id);

            if (!$product) {
                $product = Product::create([
                    'slug' => $slug,

                    
                        'sku' => $sku,
                        'the_name' => $the_name,
                        'the_short_description' => $the_short_description,
                        'the_long_description' => $the_long_description,
                        'status' => $status,
                        'thumbnail' => $thumbnail,
                ]);
            } else {
                $product->update([
                    'slug' => $slug,

                    
                        'sku' => $sku,
                        'the_name' => $the_name,
                        'the_short_description' => $the_short_description,
                        'the_long_description' => $the_long_description,
                        'status' => $status,
                        'thumbnail' => $thumbnail,
                ]);
            }
        }
    }
}
