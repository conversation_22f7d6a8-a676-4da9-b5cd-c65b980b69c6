<?php

namespace App\Imports\Tag;

use App\Models\Tag;
use Maatwebsite\Excel\Concerns\ToCollection;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\WithHeadingRow;

class FullTagsImport implements ToCollection, WithHeadingRow
{
    /**
     * @param Collection $collection
     */
    public function collection(Collection $rows)
    {
        foreach ($rows as $row) {

            $id = trim($row['id']);
            $slug = trim($row['slug']);


            $the_name = trim($row['the_name']);


            $tag = Tag::find($id);

            if (!$tag) {
                $tag = Tag::create([
                    'slug' => $slug,


                    'the_name' => $the_name,
                ]);
            } else {
                $tag->update([
                    'slug' => $slug,


                    'the_name' => $the_name,
                ]);
            }
        }
    }
}
