<?php

namespace App\Imports\Address;

use App\Models\Address;
use Maatwebsite\Excel\Concerns\ToCollection;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\WithHeadingRow;

class FullAddresssImport implements ToCollection, WithHeadingRow
{
    /**
     * @param Collection $collection
     */
    public function collection(Collection $rows)
    {
        foreach ($rows as $row) {

            $id = trim($row['id']);
            $slug = trim($row['slug']);
            
            
                        $nick_name = trim($row['nick_name']);

                        $location = trim($row['location']);

                        $latitude = trim($row['latitude']);

                        $longitude = trim($row['longitude']);

                        $note = trim($row['note']);


            $address = Address::find($id);

            if (!$address) {
                $address = Address::create([
                    'slug' => $slug,

                    
                        'nick_name' => $nick_name,
                        'location' => $location,
                        'latitude' => $latitude,
                        'longitude' => $longitude,
                        'note' => $note,
                ]);
            } else {
                $address->update([
                    'slug' => $slug,

                    
                        'nick_name' => $nick_name,
                        'location' => $location,
                        'latitude' => $latitude,
                        'longitude' => $longitude,
                        'note' => $note,
                ]);
            }
        }
    }
}
