<?php

namespace App\Imports\Item;

use App\Models\Item;
use Maatwebsite\Excel\Concerns\ToCollection;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\WithHeadingRow;

class FullItemsImport implements ToCollection, WithHeadingRow
{
    /**
     * @param Collection $collection
     */
    public function collection(Collection $rows)
    {
        foreach ($rows as $row) {

            $id = trim($row['id']);
            $slug = trim($row['slug']);
            
            
                        $price = trim($row['price']);

                        $quantity = trim($row['quantity']);

                        $note = trim($row['note']);


            $item = Item::find($id);

            if (!$item) {
                $item = Item::create([
                    'slug' => $slug,

                    
                        'price' => $price,
                        'quantity' => $quantity,
                        'note' => $note,
                ]);
            } else {
                $item->update([
                    'slug' => $slug,

                    
                        'price' => $price,
                        'quantity' => $quantity,
                        'note' => $note,
                ]);
            }
        }
    }
}
