<?php

namespace App\Imports\Story;

use App\Models\Story;
use Maatwebsite\Excel\Concerns\ToCollection;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\WithHeadingRow;

class FullStorysImport implements ToCollection, WithHeadingRow
{
    /**
     * @param Collection $collection
     */
    public function collection(Collection $rows)
    {
        foreach ($rows as $row) {

            $id = trim($row['id']);
            $slug = trim($row['slug']);
            
            
                        $the_title = trim($row['the_title']);

                        $the_description = trim($row['the_description']);

                        $year = trim($row['year']);


            $story = Story::find($id);

            if (!$story) {
                $story = Story::create([
                    'slug' => $slug,

                    
                        'the_title' => $the_title,
                        'the_description' => $the_description,
                        'year' => $year,
                ]);
            } else {
                $story->update([
                    'slug' => $slug,

                    
                        'the_title' => $the_title,
                        'the_description' => $the_description,
                        'year' => $year,
                ]);
            }
        }
    }
}
