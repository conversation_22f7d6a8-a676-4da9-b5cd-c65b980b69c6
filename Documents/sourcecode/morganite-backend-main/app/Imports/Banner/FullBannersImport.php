<?php

namespace App\Imports\Banner;

use App\Models\Banner;
use Maatwebsite\Excel\Concerns\ToCollection;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\WithHeadingRow;

class FullBannersImport implements ToCollection, WithHeadingRow
{
    /**
     * @param Collection $collection
     */
    public function collection(Collection $rows)
    {
        foreach ($rows as $row) {

            $id = trim($row['id']);
            $slug = trim($row['slug']);
            
            
                        $image = trim($row['image']);

                        $the_title = trim($row['the_title']);

                        $the_description = trim($row['the_description']);

                        $the_model = trim($row['the_model']);

                        $the_model_id = trim($row['the_model_id']);

                        $place = trim($row['place']);

                        $status = trim($row['status']);

                        $from_date = trim($row['from_date']);

                        $to_date = trim($row['to_date']);


            $banner = Banner::find($id);

            if (!$banner) {
                $banner = Banner::create([
                    'slug' => $slug,

                    
                        'image' => $image,
                        'the_title' => $the_title,
                        'the_description' => $the_description,
                        'the_model' => $the_model,
                        'the_model_id' => $the_model_id,
                        'place' => $place,
                        'status' => $status,
                        'from_date' => $from_date,
                        'to_date' => $to_date,
                ]);
            } else {
                $banner->update([
                    'slug' => $slug,

                    
                        'image' => $image,
                        'the_title' => $the_title,
                        'the_description' => $the_description,
                        'the_model' => $the_model,
                        'the_model_id' => $the_model_id,
                        'place' => $place,
                        'status' => $status,
                        'from_date' => $from_date,
                        'to_date' => $to_date,
                ]);
            }
        }
    }
}
