<?php

namespace App\Imports\Category;

use App\Models\Category;
use Maatwebsite\Excel\Concerns\ToCollection;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\WithHeadingRow;

class FullCategorysImport implements ToCollection, WithHeadingRow
{
    /**
     * @param Collection $collection
     */
    public function collection(Collection $rows)
    {
        foreach ($rows as $row) {

            $id = trim($row['id']);
            $slug = trim($row['slug']);


            $the_name = trim($row['the_name']);

            $the_description = trim($row['the_description']);

            $image = trim($row['image']);

            $banner = trim($row['banner']);


            $category = Category::find($id);

            if (!$category) {
                $category = Category::create([
                    'slug' => $slug,


                    'the_name' => $the_name,
                    'the_description' => $the_description,
                    'image' => $image,
                    'banner' => $banner,
                ]);
            } else {
                $category->update([
                    'slug' => $slug,


                    'the_name' => $the_name,
                    'the_description' => $the_description,
                    'image' => $image,
                    'banner' => $banner,
                ]);
            }
        }
    }
}
