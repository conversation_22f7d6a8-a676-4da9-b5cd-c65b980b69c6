<?php

namespace App\Imports\Order;

use App\Models\Order;
use Maatwebsite\Excel\Concerns\ToCollection;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\WithHeadingRow;

class FullOrdersImport implements ToCollection, WithHeadingRow
{
    /**
     * @param Collection $collection
     */
    public function collection(Collection $rows)
    {
        foreach ($rows as $row) {

            $id = trim($row['id']);
            $slug = trim($row['slug']);
            
            
                        $total_price = trim($row['total_price']);

                        $delivery_date = trim($row['delivery_date']);

                        $delivery_status = trim($row['delivery_status']);

                        $delivery_fee = trim($row['delivery_fee']);

                        $payment_method = trim($row['payment_method']);

                        $payment_status = trim($row['payment_status']);

                        $discount_type = trim($row['discount_type']);

                        $discount_amount = trim($row['discount_amount']);

                        $final_price = trim($row['final_price']);


            $order = Order::find($id);

            if (!$order) {
                $order = Order::create([
                    'slug' => $slug,

                    
                        'total_price' => $total_price,
                        'delivery_date' => $delivery_date,
                        'delivery_status' => $delivery_status,
                        'delivery_fee' => $delivery_fee,
                        'payment_method' => $payment_method,
                        'payment_status' => $payment_status,
                        'discount_type' => $discount_type,
                        'discount_amount' => $discount_amount,
                        'final_price' => $final_price,
                ]);
            } else {
                $order->update([
                    'slug' => $slug,

                    
                        'total_price' => $total_price,
                        'delivery_date' => $delivery_date,
                        'delivery_status' => $delivery_status,
                        'delivery_fee' => $delivery_fee,
                        'payment_method' => $payment_method,
                        'payment_status' => $payment_status,
                        'discount_type' => $discount_type,
                        'discount_amount' => $discount_amount,
                        'final_price' => $final_price,
                ]);
            }
        }
    }
}
