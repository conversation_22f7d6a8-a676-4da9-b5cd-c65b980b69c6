<?php

namespace App\Rules;

use Illuminate\Contracts\Validation\Rule;
use Illuminate\Support\Facades\Http;

class RecaptchaRule implements Rule
{
    /**
     * Create a new rule instance.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Determine if the validation rule passes.
     *
     * @param  string  $attribute
     * @param  mixed  $value
     * @return bool
     */
    public function passes($attribute, $value)
    {
        // If reCAPTCHA is not configured, skip validation
        $secretKey = config('services.recaptcha.secret_key');
        if (empty($secretKey) || $secretKey === null) {
            return true;
        }

        // If no reCAPTCHA response provided
        if (empty($value)) {
            return false;
        }

        try {
            $response = Http::asForm()->post('https://www.google.com/recaptcha/api/siteverify', [
                'secret' => config('services.recaptcha.secret_key'),
                'response' => $value,
                'remoteip' => request()->ip(),
            ]);

            $result = $response->json();

            return isset($result['success']) && $result['success'] === true;
        } catch (\Exception $e) {
            // Log the error for debugging
            \Log::error('reCAPTCHA validation failed: ' . $e->getMessage());

            // In case of API failure, allow the form submission to prevent blocking users
            // You can change this behavior based on your security requirements
            return true;
        }
    }

    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message()
    {
        return 'Please complete the reCAPTCHA verification.';
    }
}
