<?php

namespace App\View\Components\Layout;

use App\Models\Category;
use Illuminate\View\Component;

class layout extends Component
{
    public $footerContent = 'Morganite';

    public $styleList = 'all';

    public $banarText = null;

    public $banarLink = null;

    public $banarImage = null;

    public $bgNave = 'bg-transparent';



    /**
     * Create a new component instance.
     *
     * @return void
     */
    public function __construct(
        $footerContent = 'Morganite',
        $styleList = 'all',
        $banarText = null,
        $banarLink = null,
        $banarImage = null,
        $bgNave = 'bg-transparent',

    ) {

        if ($footerContent)
            $this->footerContent = $footerContent;

        if ($styleList)
            $this->styleList = $styleList;

        if ($banarText)
            $this->banarText = $banarText;

        if ($banarLink)
            $this->banarLink = $banarLink;

        if ($banarImage)
            $this->banarImage = $banarImage;

        if ($bgNave)
            $this->bgNave = $bgNave;
    }

    /**
     * Get the view / contents that represent the component.
     *
     * @return \Illuminate\Contracts\View\View|\Closure|string
     */
    public function render()

    {

        return view('components.layout.layout');
    }
}
