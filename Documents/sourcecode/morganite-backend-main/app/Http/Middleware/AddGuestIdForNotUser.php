<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class AddGuestIdForNotUser
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        if (!auth()->check())
            if (!session()->has('guest_id'))
                session()->put('guest_id', date('ymdhis') . rand(10000, 99999));

        return $next($request);
    }
}
