<?php

namespace App\Http\Livewire\Product;

use App\Http\Controllers\HomeController;
use App\Models\Appimage;
use App\Models\AttributeValue;
use App\Models\Category;
use App\Models\Product;
use App\Models\Tag;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Route;
use Livewire\Component;
use Livewire\WithFileUploads;
use Livewire\WithPagination;

class ProductIndex extends Component
{
    use WithPagination;
    use WithFileUploads;

    protected $paginationTheme = 'bootstrap';

    public $perPage = 25;
    public $search = '';
    public $orderBy = 'id';
    public $orderWay = 'desc';
    public $showColumn;

    public $all;
    public $fromDate = null;
    public $toDate = null;
    public $byDate = 'created_at';

    public $selectedProducts = [];

    public Product $product;
    private $products;
    public $user;

    public $categories = [];
    public $selectedCategories = [];

    public $tags = [];

    public $selectedTags = [];


    public $discount_type = '';
    public $discount_amount = 0.0;
    public $quantity_in_stock = '';

    public $images = [];






    public $url;
    public $admin_view_status = '';
    public function mount($admin_view_status = '')
    {
        $this->url = Route::current()->getName();
        $this->admin_view_status = $admin_view_status;
        $this->user = Auth::user();

        $this->product = new Product();

        $this->categories = Category::orderBy('parent_id')->orderBy('sort')->get();

        $this->tags = Tag::orderBy('sort')->get();

        $this->all = true;
        // $this->fromDate = date('Y-m-d', strtotime("-5 days"));
        $this->toDate = date('Y-m-d');




        $this->showColumn = collect([
            'id' => false,
            'slug' => false,


            'sku' => true,
            'the_name' => true,
            'the_short_description' => false,
            'the_long_description' => false,
            'status' => true,
            'thumbnail' => true,

            // 'status' => false,
            'date' => false,
            'time' => false,
        ]);
    }

    public $slug;
    public $product_id, $sku, $the_name, $the_short_description, $the_long_description, $status, $thumbnail;

    public $attributevalue_id, $the_description, $price, $manage_stock, $discount_from_date, $discount_to_date, $attribute_group_id;
    public $updateMode = false;

    private function resetInputFields()
    {
        $this->slug = '';

        $this->sku = '';
        $this->the_name = '';
        $this->the_short_description = '';
        $this->the_long_description = '';
        $this->status = '';
        $this->thumbnail = '';

        //
        //

        $this->the_description = '';
        $this->price = '';
        $this->discount_type = '';
        $this->discount_amount = 0.0;
        $this->manage_stock = '';
        $this->quantity_in_stock = '';
        $this->discount_from_date = '';
        $this->discount_to_date = '';

        $this->attribute_group_id = null;
        $this->product_id = null;

        $this->images = [];
    }


    public function rules()
    {
        return [
            // 'sku' => 'required',
            'the_name' => 'required',
            'the_short_description' => 'required',
            'the_long_description' => 'required',
            'status' => 'required',
            'thumbnail' => 'required',
            // 'the_description' => 'required',
            'price' => 'required',
            // 'discount_type' => 'required',
            // 'discount_amount' => 'required',
            'manage_stock' => 'required',
            // 'quantity_in_stock' => 'required',
            // 'discount_from_date' => 'required',
            // 'discount_to_date' => 'required',

            // 'attribute_group_id' => 'required',
            // 'product_id' => 'required',
        ];
    }

    public function updated($propertyName)
    {
        $this->validateOnly($propertyName);
    }

    public function store()
    {
        $validatedData = $this->validate();

        $product =  Product::create([
            'slug' => $this->slug,

            'sku' => $this->sku,
            'the_name' => $this->the_name,
            'the_short_description' => $this->the_short_description,
            'the_long_description' => $this->the_long_description,
            'status' => $this->status,
            'thumbnail' => HomeController::saveImageWeb($this->thumbnail, 'product'),

        ]);

        AttributeValue::create([
            'sku' => $this->sku,
            'the_name' => $this->the_name,
            // 'the_description' => $this->the_description,
            'price' => $this->price,
            'discount_type' => $this->discount_type,
            'discount_amount' => $this->discount_amount,
            'manage_stock' => $this->manage_stock,
            'quantity_in_stock' => $this->quantity_in_stock,
            'discount_from_date' => $this->discount_from_date,
            'discount_to_date' => $this->discount_to_date,
            'status' => $this->status,
            // 'attribute_group_id' => $this->attribute_group_id,
            'product_id' => $product->id,
        ]);

        foreach ($this->images as $image) {
            Appimage::create([
                // 'slug' => $this->slug,
                'the_model_name' => 'product',
                'the_model_id' => $product->id,
                // 'place' => $this->place,
                'image' => HomeController::saveImageWeb($image, 'appimage'),
            ]);
        }

        $product->categories()->sync($this->selectedCategories);

        $product->tags()->sync($this->selectedTags);

        session()->flash('message', 'Product Created Successfully.');

        $this->resetInputFields();

        // $this->emit('close-model'); // Close model to using to jquery
    }

    public function edit($id)
    {
        $this->updateMode = true;
        $product = Product::where('id', $id)->first();
        $this->product_id = $id;
        $this->slug = $product->slug;


        $this->sku = $product->sku;
        $this->the_name = $product->the_name;
        $this->the_short_description = $product->the_short_description;
        $this->the_long_description = $product->the_long_description;
        $this->status = $product->status;

        //
        // $this->sku = $attributevalue->sku;
        // $this->the_name = $attributevalue->the_name;
        // $this->the_description = $attributevalue->the_description;
        $this->price = $product->attribute_value->price;
        $this->discount_type = $product->attribute_value->discount_type;
        $this->discount_amount = $product->attribute_value->discount_amount;
        $this->manage_stock = $product->attribute_value->manage_stock;
        $this->quantity_in_stock = $product->attribute_value->quantity_in_stock;
        $this->discount_from_date = $product->attribute_value->discount_from_date;
        $this->discount_to_date = $product->attribute_value->discount_to_date;
        // $this->status = $attributevalue->status;
        // $this->attribute_group_id = $attributevalue->attribute_group_id;
        $this->product_id = $product->id;
        //

        $this->selectedCategories = $product->categories->pluck('id');
        $this->selectedTags = $product->tags->pluck('id');

        // $this->thumbnail = $product->thumbnail;
    }

    public function cancel()
    {
        $this->updateMode = false;
        $this->resetInputFields();
    }

    public function update()
    {

        if ($this->product_id) {
            $product = Product::find($this->product_id);
            $product->update([
                'slug' => $this->slug,

                'sku' => $this->sku,
                'the_name' => $this->the_name,
                'the_short_description' => $this->the_short_description,
                'the_long_description' => $this->the_long_description,
                'status' => $this->status,
                'thumbnail' => $this->thumbnail ? HomeController::saveImageWeb($this->thumbnail, 'product') : $product->thumbnail,

                'discount_type' => $this->discount_type,
                'discount_amount' => $this->discount_amount,
                'manage_stock' => $this->manage_stock,
                'quantity_in_stock' => $this->quantity_in_stock,
            ]);

            $product->attribute_value()->update([
                'sku' => $this->sku,
                'the_name' => $this->the_name,
                // 'the_description' => $this->the_description,
                'price' => $this->price,
                'discount_type' => $this->discount_type,
                'discount_amount' => $this->discount_amount,
                'manage_stock' => $this->manage_stock,
                'quantity_in_stock' => $this->quantity_in_stock,
                'discount_from_date' => $this->discount_from_date,
                'discount_to_date' => $this->discount_to_date,
                'status' => $this->status,
                // 'attribute_group_id' => $this->attribute_group_id,
                // 'product_id' => $product->id,
            ]);


            $product->categories()->sync($this->selectedCategories);

            $product->tags()->sync($this->selectedTags);

            $this->updateMode = false;
            session()->flash('message', 'Product Updated Successfully.');
            $this->resetInputFields();
        }

        $this->emit('close-model'); // Close model to using to jquery
    }

    public function delete($id)
    {
        if ($id) {
            $product = Product::find($id);

            $product->delete();

            session()->flash('message', 'Product Deleted Successfully.');
        }
    }

    public function restore($id)
    {
        if ($id) {
            $product = Product::withTrashed()->find($id);

            $product->restore();

            session()->flash('message', 'Product Recovered Successfully.');
        }
    }

    public function updatedFromDate($fromDate)
    {
        $this->all = false;
    }

    public function updatedToDate($toDate)
    {
        $this->all = false;
    }

    public function clearFilter()
    {
        $this->all = true;
        // $this->fromDate = date('Y-m-d', strtotime("-5 days"));
        $this->byDate = 'created_at';
        $this->fromDate = '';
        $this->toDate = date('Y-m-d');
    }




    public function gotoPage($page)
    {
        $this->setPage($page);
        $this->emit('gotoTop');
    }

    public function nextPage()
    {
        $this->setPage($this->page + 1);
        $this->emit('gotoTop');
    }

    public function previousPage()
    {
        $this->setPage(max($this->page - 1, 1));
        $this->emit('gotoTop');
    }

    public function render()
    {
        $products = Product::livewireSearch($this->search);

        if ($this->all == false)
            $products = $products->whereBetween($this->byDate, [$this->fromDate . ' 00:00:00', $this->toDate . ' 23:59:59']);




        if ($this->admin_view_status == 'deleted')
            $products = $products->onlyTrashed();


        $products = $products->orderBy($this->orderBy, $this->orderWay)
            ->paginate($this->perPage);

        return view('livewire.product.product-index', compact('products'));
    }
}
