<?php

namespace App\Http\Livewire\Firebasenotification;

use App\Models\Firebasenotification;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Route;
use Livewire\Component;
use Livewire\WithPagination;

class FirebasenotificationIndex extends Component
{
    use WithPagination;

    protected $paginationTheme = 'bootstrap';

    public $perPage = 25;
    public $search = '';
    public $orderBy = 'id';
    public $orderWay = 'desc';
    public $showColumn;

    public $all;
    public $fromDate = null;
    public $toDate = null;
    public $byDate = 'created_at';

    public $selectedFirebaseNotifications = [];

    public FirebaseNotification $firebasenotification;
    private $firebasenotifications;
    public $user;





    public $url;
    public $admin_view_status = '';
    public function mount($admin_view_status = '')
    {
        $this->url = Route::current()->getName();
        $this->admin_view_status = $admin_view_status;
        $this->user = Auth::user();

        $this->firebasenotification = new FirebaseNotification();

        $this->all = true;
        // $this->fromDate = date('Y-m-d', strtotime("-5 days"));
        $this->toDate = date('Y-m-d');




        $this->showColumn = collect([
            'id' => false,
            'slug' => false,


            'user_id' => true,
            'notification_text_id' => true,
            'image' => true,
            'the_page' => true,
            'title' => true,
            'body' => true,
            'data_id' => true,
            'seen' => true,
            'seen_at' => true,

            // 'status' => false,
            'date' => true,
            'time' => false,
        ]);
    }

    public $slug;
    public $firebasenotification_id, $user_id, $notification_text_id, $image, $the_page, $title, $body, $data_id, $seen, $seen_at;
    public $updateMode = false;

    private function resetInputFields()
    {
        $this->slug = '';

        $this->user_id = '';
        $this->notification_text_id = '';
        $this->image = '';
        $this->the_page = '';
        $this->title = '';
        $this->body = '';
        $this->data_id = '';
        $this->seen = '';
        $this->seen_at = '';
    }


    public function rules()
    {
        return [
            // 'patient_id' => "required|unique:patient_studies,patient_id,$this->patientstudy_id,id,study_id,$this->study_id,deleted_at,NULL",
            // 'slug' => $this-slug,


            'user_id' => 'required',
            'notification_text_id' => 'required',
            'image' => 'required',
            'the_page' => 'required',
            'title' => 'required',
            'body' => 'required',
            'data_id' => 'required',
            'seen' => 'required',
            'seen_at' => 'required',
        ];
    }

    public function updated($propertyName)
    {
        $this->validateOnly($propertyName);
    }

    public function store()
    {
        $validatedData = $this->validate();

        FirebaseNotification::create([
            'slug' => $this->slug,

            'user_id' => $this->user_id,
            'notification_text_id' => $this->notification_text_id,
            'image' => $this->image,
            'the_page' => $this->the_page,
            'title' => $this->title,
            'body' => $this->body,
            'data_id' => $this->data_id,
            'seen' => $this->seen,
            'seen_at' => $this->seen_at,
        ]);

        session()->flash('message', 'FirebaseNotification Created Successfully.');

        $this->resetInputFields();

        // $this->emit('close-model'); // Close model to using to jquery
    }

    public function edit($id)
    {
        $this->updateMode = true;
        $firebasenotification = FirebaseNotification::where('id', $id)->first();
        $this->firebasenotification_id = $id;
        $this->slug = $firebasenotification->slug;


        $this->user_id = $firebasenotification->user_id;
        $this->notification_text_id = $firebasenotification->notification_text_id;
        $this->image = $firebasenotification->image;
        $this->the_page = $firebasenotification->the_page;
        $this->title = $firebasenotification->title;
        $this->body = $firebasenotification->body;
        $this->data_id = $firebasenotification->data_id;
        $this->seen = $firebasenotification->seen;
        $this->seen_at = $firebasenotification->seen_at;
    }

    public function cancel()
    {
        $this->updateMode = false;
        $this->resetInputFields();
    }

    public function update()
    {

        if ($this->firebasenotification_id) {
            $firebasenotification = FirebaseNotification::find($this->firebasenotification_id);
            $firebasenotification->update([
                'slug' => $this->slug,

                'user_id' => $this->user_id,
                'notification_text_id' => $this->notification_text_id,
                'image' => $this->image,
                'the_page' => $this->the_page,
                'title' => $this->title,
                'body' => $this->body,
                'data_id' => $this->data_id,
                'seen' => $this->seen,
                'seen_at' => $this->seen_at,
            ]);

            $this->updateMode = false;
            session()->flash('message', 'FirebaseNotification Updated Successfully.');
            $this->resetInputFields();
        }

        $this->emit('close-model'); // Close model to using to jquery
    }

    public function delete($id)
    {
        if ($id) {
            $firebasenotification = FirebaseNotification::find($id);

            $firebasenotification->delete();

            session()->flash('message', 'FirebaseNotification Deleted Successfully.');
        }
    }

    public function restore($id)
    {
        if ($id) {
            $firebasenotification = FirebaseNotification::withTrashed()->find($id);

            $firebasenotification->restore();

            session()->flash('message', 'FirebaseNotification Recovered Successfully.');
        }
    }

    public function updatedFromDate($fromDate)
    {
        $this->all = false;
    }

    public function updatedToDate($toDate)
    {
        $this->all = false;
    }

    public function clearFilter()
    {
        $this->all = true;
        // $this->fromDate = date('Y-m-d', strtotime("-5 days"));
        $this->byDate = 'created_at';
        $this->fromDate = '';
        $this->toDate = date('Y-m-d');
    }




    public function gotoPage($page)
    {
        $this->setPage($page);
        $this->emit('gotoTop');
    }

    public function nextPage()
    {
        $this->setPage($this->page + 1);
        $this->emit('gotoTop');
    }

    public function previousPage()
    {
        $this->setPage(max($this->page - 1, 1));
        $this->emit('gotoTop');
    }

    public function render()
    {
        $firebasenotifications = FirebaseNotification::livewireSearch($this->search);

        if ($this->all == false)
            $firebasenotifications = $firebasenotifications->whereBetween($this->byDate, [$this->fromDate . ' 00:00:00', $this->toDate . ' 23:59:59']);




        if ($this->admin_view_status == 'deleted')
            $firebasenotifications = $firebasenotifications->onlyTrashed();


        $firebasenotifications = $firebasenotifications->orderBy($this->orderBy, $this->orderWay)
            ->paginate($this->perPage);

        return view('livewire.firebasenotification.firebasenotification-index', compact('firebasenotifications'));
    }
}
