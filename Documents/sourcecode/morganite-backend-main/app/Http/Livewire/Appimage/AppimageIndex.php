<?php

namespace App\Http\Livewire\Appimage;

use App\Http\Controllers\HomeController;
use App\Models\Appimage;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Route;
use Livewire\Component;
use Livewire\WithFileUploads;
use Livewire\WithPagination;

class AppimageIndex extends Component
{
    use WithPagination;
    use WithFileUploads;

    protected $paginationTheme = 'bootstrap';

    public $perPage = 25;
    public $search = '';
    public $orderBy = 'id';
    public $orderWay = 'desc';
    public $showColumn;

    public $all;
    public $fromDate = null;
    public $toDate = null;
    public $byDate = 'created_at';

    public $selectedAppimages = [];

    public Appimage $appimage;
    private $appimages;
    public $user;





    public $url;
    public $admin_view_status = '';
    public function mount($admin_view_status = '', $the_model_name = null, $the_model_id = null)
    {
        $this->url = Route::current()->getName();
        $this->admin_view_status = $admin_view_status;
        $this->user = Auth::user();

        $this->appimage = new Appimage();

        $this->the_model_name = $the_model_name;
        $this->the_model_id = $the_model_id;

        $this->all = true;
        // $this->fromDate = date('Y-m-d', strtotime("-5 days"));
        $this->toDate = date('Y-m-d');




        $this->showColumn = collect([
            'id' => false,
            'slug' => false,


            'the_model_name' => false,
            'the_model_id' => false,
            'place' => false,
            'image' => true,

            // 'status' => false,
            'date' => true,
            'time' => false,
        ]);
    }

    public $slug;
    public $appimage_id, $the_model_name, $the_model_id, $place, $image;
    public $updateMode = false;

    private function resetInputFields()
    {
        $this->slug = '';

        // $this->the_model_name = '';
        // $this->the_model_id = null;
        // $this->place = '';
        $this->image = '';
    }


    public function rules()
    {
        return [
            // 'patient_id' => "required|unique:patient_studies,patient_id,$this->patientstudy_id,id,study_id,$this->study_id,deleted_at,NULL",
            // 'slug' => $this-slug,


            // 'the_model_name' => 'required',
            // 'the_model_id' => 'required',
            // 'place' => 'required',
            'image' => 'required',
        ];
    }

    public function updated($propertyName)
    {
        $this->validateOnly($propertyName);
    }

    public function store()
    {
        $validatedData = $this->validate();

        Appimage::create([
            'slug' => $this->slug,

            'the_model_name' => $this->the_model_name,
            'the_model_id' => $this->the_model_id,
            'place' => $this->place,
            'image' => HomeController::saveImageWeb($this->image, 'appimage'),
        ]);

        session()->flash('message', 'Appimage Created Successfully.');

        $this->resetInputFields();

        // $this->emit('close-model'); // Close model to using to jquery
    }

    public function edit($id)
    {
        $this->updateMode = true;
        $appimage = Appimage::where('id', $id)->first();
        $this->appimage_id = $id;
        $this->slug = $appimage->slug;


        $this->the_model_name = $appimage->the_model_name;
        $this->the_model_id = $appimage->the_model_id;
        $this->place = $appimage->place;
        $this->image = $appimage->image;
    }

    public function cancel()
    {
        $this->updateMode = false;
        $this->resetInputFields();
    }

    public function update()
    {

        if ($this->appimage_id) {
            $appimage = Appimage::find($this->appimage_id);
            $appimage->update([
                'slug' => $this->slug,

                'the_model_name' => $this->the_model_name,
                'the_model_id' => $this->the_model_id,
                'place' => $this->place,
                'image' => $this->image ? HomeController::saveImageWeb($this->image, 'appimage') : $appimage->image,
            ]);

            $this->updateMode = false;
            session()->flash('message', 'Appimage Updated Successfully.');
            $this->resetInputFields();
        }

        $this->emit('close-model'); // Close model to using to jquery
    }

    public function delete($id)
    {
        if ($id) {
            $appimage = Appimage::find($id);

            $appimage->delete();

            session()->flash('message', 'Appimage Deleted Successfully.');
        }
    }

    public function restore($id)
    {
        if ($id) {
            $appimage = Appimage::withTrashed()->find($id);

            $appimage->restore();

            session()->flash('message', 'Appimage Recovered Successfully.');
        }
    }

    public function updatedFromDate($fromDate)
    {
        $this->all = false;
    }

    public function updatedToDate($toDate)
    {
        $this->all = false;
    }

    public function clearFilter()
    {
        $this->all = true;
        // $this->fromDate = date('Y-m-d', strtotime("-5 days"));
        $this->byDate = 'created_at';
        $this->fromDate = '';
        $this->toDate = date('Y-m-d');
    }




    public function gotoPage($page)
    {
        $this->setPage($page);
        $this->emit('gotoTop');
    }

    public function nextPage()
    {
        $this->setPage($this->page + 1);
        $this->emit('gotoTop');
    }

    public function previousPage()
    {
        $this->setPage(max($this->page - 1, 1));
        $this->emit('gotoTop');
    }

    public function render()
    {
        $appimages = Appimage::livewireSearch($this->search);

        if ($this->all == false)
            $appimages = $appimages->whereBetween($this->byDate, [$this->fromDate . ' 00:00:00', $this->toDate . ' 23:59:59']);


        if ($this->the_model_name != null)
            $appimages = $appimages->where('the_model_name', $this->the_model_name);

        if ($this->the_model_id != null)
            $appimages = $appimages->where('the_model_id', $this->the_model_id);


        if ($this->admin_view_status == 'deleted')
            $appimages = $appimages->onlyTrashed();


        $appimages = $appimages->orderBy($this->orderBy, $this->orderWay)
            ->paginate($this->perPage);

        return view('livewire.appimage.appimage-index', compact('appimages'));
    }
}
