<?php

namespace App\Http\Livewire\Web;

use App\Models\Category;
use App\Models\Product;
use Livewire\Component;
use Livewire\WithPagination;



class ProductFilter extends Component
{

    use WithPagination;
    protected $paginationTheme = 'bootstrap';

    public $category = 0;

    public function mount()
    {
        $this->category = request()->get('category', 0);
    }

    public function setCategory($id)
    {
        $this->category = $id;

        if ($id > 0) {
            $category = Category::find($id);
        }
    }


    public function render()

    // TODO AWWAD
    {

        $products = Product::where('show', true);

        if ($this->category != 0) {
            $products = $products->whereHas('categories', function ($query) {
                $query->where('id', $this->category);
            });
        }
        

        $products = $products->orderBy('id', 'desc')->get();

        $mainCategories = Category::whereNullOrEmptyOrZero('parent_id')->get();

        $subCategories = Category::where('parent_id', '!=', null)->get();
        // dd($this->category);


        return view('livewire.web.product-filter', compact('products', 'mainCategories', 'subCategories'));
    }
}
