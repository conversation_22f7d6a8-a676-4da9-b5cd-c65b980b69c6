<?php

namespace App\Http\Livewire\Web;

use App\Models\Order;
use Livewire\Component;

class WebTableOrder extends Component
{

    public function deleteOrderTable($id)
    {
        $order = Order::find($id);

        $order->delete();
    }


    public function render()
    {
        $user = auth()->user();
        $orders = Order::where('user_id', $user->id)
            ->where('order_status_id', '!=', 1)
            ->get();
        return view('livewire.web.web-table-order', compact('orders'));
    }
}
