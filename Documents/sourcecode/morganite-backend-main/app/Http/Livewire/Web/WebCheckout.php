<?php

namespace App\Http\Livewire\Web;

use App\Jobs\SendNewOrderToAdmin;
use App\Models\Address;
use App\Models\Area;
use App\Models\City;
use App\Models\Country;
use App\Models\Order;
use Livewire\Component;

class WebCheckout extends Component
{
    public $guest_id;

    public $order;

    public $location_name;
    public $note;

    public $countries = [];

    public $country_id;

    public  $cites = [];

    public $city_id;

    public $areas = [];

    public $area_id;

    public $payment_method;


    public $user;



    public function mount()
    {
        $this->guest_id = session()->get('guest_id', 0);

        $this->countries = Country::get();
    }

    public function updatedCountryId($val)
    {
        if ($val > 0) {
            $this->cites = City::where('country_id', $val)->get();
            $this->areas = [];
            $this->city_id = null;
            $this->area_id = null;
        } else {
            $this->cites = [];
            $this->areas = [];
            $this->city_id = null;
            $this->area_id = null;
        }
    }

    public function updatedCityId($val)
    {
        if ($val > 0)
            $this->areas = Area::where('city_id', $val)->get();
        else
            $this->areas = [];
    }


    public function finalStepPayemnt()
    {
        $order = Order::find($this->order->id);

        if ($order->address) {
            $order->address()->update([
                'nick_name' => $this->location_name,
                // 'location' => '',
                'country_id' => $this->country_id,
                'city_id' => $this->city_id,
                'area_id' => $this->area_id,
                'note' => $this->note,
            ]);
        } else {
            $address = Address::create([
                'user_id' => $order->user->id,
                'nick_name' => $this->location_name,
                // 'location' => '',
                'country_id' => $this->country_id,
                'city_id' => $this->city_id,
                'area_id' => $this->area_id,
                'note' => $this->note,
            ]);
            $order->update([
                'address_id' => $address->id,
            ]);
        }

        $order->update([
            'payment_method' => $this->payment_method,
            'order_status_id' => 2
        ]);


        SendNewOrderToAdmin::dispatchAfterResponse($order);

        return redirect()->route('web.acoount_orders');
    }

    public function render()
    {
        if (auth()->user())
            $this->order = Order::where('user_id', auth()->user()->id)->where('order_status_id', 1)->latest()->first();
        else
            $this->order = Order::where('add_by', $this->guest_id)->where('order_status_id', 1)->latest()->first();

        return view('livewire.web.web-checkout');
    }
}
