<?php

namespace App\Http\Livewire\Web;

use App\Models\Item;
use App\Models\Order;
use Livewire\Component;

class CartOffcanvas extends Component
{

    public $order;
    public $guest_id;

    // public $items;

    public function updateTheItems()
    {
        $this->guest_id = session()->get('guest_id', 0);

        if (auth()->user())
            $this->order = Order::where('user_id', auth()->user()->id)->where('order_status_id', 1)->latest()->first();
        else
            $this->order = Order::where('add_by', $this->guest_id)->where('order_status_id', 1)->latest()->first();
    }
    protected $listeners = [
        'refreshItems' => 'updateTheItems'
    ];

    public function deleteItems($id)
    {
        $item = Item::find($id);

        $item->delete();

        $this->updateTheItems();
    }

    public function mount()
    {
        $this->updateTheItems();
    }
    public function render()
    {
        return view('livewire.web.cart-offcanvas');
    }
}
