<?php

namespace App\Http\Livewire\Firebasenotificationtext;

use App\Models\FirebasenotificationText;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Route;
use Livewire\Component;
use Livewire\WithPagination;

class FirebasenotificationtextIndex extends Component
{
    use WithPagination;

    protected $paginationTheme = 'bootstrap';

    public $perPage = 25;
    public $search = '';
    public $orderBy = 'id';
    public $orderWay = 'desc';
    public $showColumn;

    public $all;
    public $fromDate = null;
    public $toDate = null;
    public $byDate = 'created_at';

    public $selectedFirebaseNotificationTexts = [];

    public FirebaseNotificationText $firebasenotificationtext;
    private $firebasenotificationtexts;
    public $user;





    public $url;
    public $admin_view_status = '';
    public function mount($admin_view_status = '')
    {
        $this->url = Route::current()->getName();
        $this->admin_view_status = $admin_view_status;
        $this->user = Auth::user();

        $this->firebasenotificationtext = new FirebaseNotificationText();

        $this->all = true;
        // $this->fromDate = date('Y-m-d', strtotime("-5 days"));
        $this->toDate = date('Y-m-d');




        $this->showColumn = collect([
            'id' => false,
            'slug' => false,


            'the_title' => true,
            'the_text' => true,

            // 'status' => false,
            'date' => true,
            'time' => false,
        ]);
    }

    public $slug;
    public $firebasenotificationtext_id, $the_title, $the_text;
    public $updateMode = false;

    private function resetInputFields()
    {
        $this->slug = '';

        $this->the_title = '';
        $this->the_text = '';
    }


    public function rules()
    {
        return [
            // 'patient_id' => "required|unique:patient_studies,patient_id,$this->patientstudy_id,id,study_id,$this->study_id,deleted_at,NULL",
            // 'slug' => $this-slug,


            'the_title' => 'required',
            'the_text' => 'required',
        ];
    }

    public function updated($propertyName)
    {
        $this->validateOnly($propertyName);
    }

    public function store()
    {
        $validatedData = $this->validate();

        FirebaseNotificationText::create([
            'slug' => $this->slug,

            'the_title' => $this->the_title,
            'the_text' => $this->the_text,
        ]);

        session()->flash('message', 'FirebaseNotificationText Created Successfully.');

        $this->resetInputFields();

        // $this->emit('close-model'); // Close model to using to jquery
    }

    public function edit($id)
    {
        $this->updateMode = true;
        $firebasenotificationtext = FirebaseNotificationText::where('id', $id)->first();
        $this->firebasenotificationtext_id = $id;
        $this->slug = $firebasenotificationtext->slug;


        $this->the_title = $firebasenotificationtext->the_title;
        $this->the_text = $firebasenotificationtext->the_text;
    }

    public function cancel()
    {
        $this->updateMode = false;
        $this->resetInputFields();
    }

    public function update()
    {

        if ($this->firebasenotificationtext_id) {
            $firebasenotificationtext = FirebaseNotificationText::find($this->firebasenotificationtext_id);
            $firebasenotificationtext->update([
                'slug' => $this->slug,

                'the_title' => $this->the_title,
                'the_text' => $this->the_text,
            ]);

            $this->updateMode = false;
            session()->flash('message', 'FirebaseNotificationText Updated Successfully.');
            $this->resetInputFields();
        }

        $this->emit('close-model'); // Close model to using to jquery
    }

    public function delete($id)
    {
        if ($id) {
            $firebasenotificationtext = FirebaseNotificationText::find($id);

            $firebasenotificationtext->delete();

            session()->flash('message', 'FirebaseNotificationText Deleted Successfully.');
        }
    }

    public function restore($id)
    {
        if ($id) {
            $firebasenotificationtext = FirebaseNotificationText::withTrashed()->find($id);

            $firebasenotificationtext->restore();

            session()->flash('message', 'FirebaseNotificationText Recovered Successfully.');
        }
    }

    public function updatedFromDate($fromDate)
    {
        $this->all = false;
    }

    public function updatedToDate($toDate)
    {
        $this->all = false;
    }

    public function clearFilter()
    {
        $this->all = true;
        // $this->fromDate = date('Y-m-d', strtotime("-5 days"));
        $this->byDate = 'created_at';
        $this->fromDate = '';
        $this->toDate = date('Y-m-d');
    }




    public function gotoPage($page)
    {
        $this->setPage($page);
        $this->emit('gotoTop');
    }

    public function nextPage()
    {
        $this->setPage($this->page + 1);
        $this->emit('gotoTop');
    }

    public function previousPage()
    {
        $this->setPage(max($this->page - 1, 1));
        $this->emit('gotoTop');
    }

    public function render()
    {
        $firebasenotificationtexts = FirebaseNotificationText::livewireSearch($this->search);

        if ($this->all == false)
            $firebasenotificationtexts = $firebasenotificationtexts->whereBetween($this->byDate, [$this->fromDate . ' 00:00:00', $this->toDate . ' 23:59:59']);




        if ($this->admin_view_status == 'deleted')
            $firebasenotificationtexts = $firebasenotificationtexts->onlyTrashed();


        $firebasenotificationtexts = $firebasenotificationtexts->orderBy($this->orderBy, $this->orderWay)
            ->paginate($this->perPage);

        return view('livewire.firebasenotificationtext.firebasenotificationtext-index', compact('firebasenotificationtexts'));
    }
}
