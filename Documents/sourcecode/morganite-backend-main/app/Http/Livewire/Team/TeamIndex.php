<?php

namespace App\Http\Livewire\Team;

use App\Http\Controllers\HomeController;
use App\Models\Team;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Route;
use Livewire\Component;
use Livewire\WithFileUploads;
use Livewire\WithPagination;

class TeamIndex extends Component
{
    use WithPagination;

    use WithFileUploads;

    protected $paginationTheme = 'bootstrap';

    public $perPage = 25;
    public $search = '';
    public $orderBy = 'id';
    public $orderWay = 'desc';
    public $showColumn;

    public $all;
    public $fromDate = null;
    public $toDate = null;
    public $byDate = 'created_at';

    public $selectedTeams = [];

    public Team $team;
    private $teams;
    public $user;





    public $url;
    public $admin_view_status = '';
    public function mount($admin_view_status = '')
    {
        $this->url = Route::current()->getName();
        $this->admin_view_status = $admin_view_status;
        $this->user = Auth::user();

        $this->team = new Team();

        $this->all = true;
        // $this->fromDate = date('Y-m-d', strtotime("-5 days"));
        $this->toDate = date('Y-m-d');




        $this->showColumn = collect([
            'id' => false,
            'slug' => false,


            'the_name' => true,
            'the_sub_title' => false,
            'the_bio' => false,
            'image' => true,
            'phone' => false,
            'email' => true,
            'fb_link' => false,
            'tw_link' => false,
            'in_link' => false,
            'ln_link' => false,

            // 'status' => false,
            'date' => true,
            'time' => false,
        ]);
    }

    public $slug;
    public $team_id, $the_name, $the_sub_title, $the_bio, $image, $phone, $email, $fb_link, $tw_link, $in_link, $ln_link;
    public $updateMode = false;

    private function resetInputFields()
    {
        $this->slug = '';

        $this->the_name = '';
        $this->the_sub_title = '';
        $this->the_bio = '';
        $this->image = '';
        $this->phone = '';
        $this->email = '';
        $this->fb_link = '';
        $this->tw_link = '';
        $this->in_link = '';
        $this->ln_link = '';
    }


    public function rules()
    {
        return [
            // 'patient_id' => "required|unique:patient_studies,patient_id,$this->patientstudy_id,id,study_id,$this->study_id,deleted_at,NULL",
            // 'slug' => $this-slug,


            'the_name' => 'required',
            'the_sub_title' => 'required',
            'the_bio' => 'required',
            'image' => 'required',
            'phone' => 'required',
            'email' => 'required',
            'fb_link' => 'required',
            'tw_link' => 'required',
            'in_link' => 'required',
            'ln_link' => 'required',
        ];
    }

    public function updated($propertyName)
    {
        $this->validateOnly($propertyName);
    }

    public function store()
    {
        $validatedData = $this->validate();

        Team::create([
            'slug' => $this->slug,

            'the_name' => $this->the_name,
            'the_sub_title' => $this->the_sub_title,
            'the_bio' => $this->the_bio,
            'image' => HomeController::saveImageWeb($this->image, 'team'),
            'phone' => $this->phone,
            'email' => $this->email,
            'fb_link' => $this->fb_link,
            'tw_link' => $this->tw_link,
            'in_link' => $this->in_link,
            'ln_link' => $this->ln_link,
        ]);

        session()->flash('message', 'Team Created Successfully.');

        $this->resetInputFields();

        // $this->emit('close-model'); // Close model to using to jquery
    }

    public function edit($id)
    {
        $this->updateMode = true;
        $team = Team::where('id', $id)->first();
        $this->team_id = $id;
        $this->slug = $team->slug;


        $this->the_name = $team->the_name;
        $this->the_sub_title = $team->the_sub_title;
        $this->the_bio = $team->the_bio;
        // $this->image = $team->image;
        $this->phone = $team->phone;
        $this->email = $team->email;
        $this->fb_link = $team->fb_link;
        $this->tw_link = $team->tw_link;
        $this->in_link = $team->in_link;
        $this->ln_link = $team->ln_link;
    }

    public function cancel()
    {
        $this->updateMode = false;
        $this->resetInputFields();
    }

    public function update()
    {

        if ($this->team_id) {
            $team = Team::find($this->team_id);
            $team->update([
                'slug' => $this->slug,

                'the_name' => $this->the_name,
                'the_sub_title' => $this->the_sub_title,
                'the_bio' => $this->the_bio,
                'image' => $this->image ? HomeController::saveImageWeb($this->image, 'team') : $team->image,
                'phone' => $this->phone,
                'email' => $this->email,
                'fb_link' => $this->fb_link,
                'tw_link' => $this->tw_link,
                'in_link' => $this->in_link,
                'ln_link' => $this->ln_link,
            ]);

            $this->updateMode = false;
            session()->flash('message', 'Team Updated Successfully.');
            $this->resetInputFields();
        }

        $this->emit('close-model'); // Close model to using to jquery
    }

    public function delete($id)
    {
        if ($id) {
            $team = Team::find($id);

            $team->delete();

            session()->flash('message', 'Team Deleted Successfully.');
        }
    }

    public function restore($id)
    {
        if ($id) {
            $team = Team::withTrashed()->find($id);

            $team->restore();

            session()->flash('message', 'Team Recovered Successfully.');
        }
    }

    public function updatedFromDate($fromDate)
    {
        $this->all = false;
    }

    public function updatedToDate($toDate)
    {
        $this->all = false;
    }

    public function clearFilter()
    {
        $this->all = true;
        // $this->fromDate = date('Y-m-d', strtotime("-5 days"));
        $this->byDate = 'created_at';
        $this->fromDate = '';
        $this->toDate = date('Y-m-d');
    }




    public function gotoPage($page)
    {
        $this->setPage($page);
        $this->emit('gotoTop');
    }

    public function nextPage()
    {
        $this->setPage($this->page + 1);
        $this->emit('gotoTop');
    }

    public function previousPage()
    {
        $this->setPage(max($this->page - 1, 1));
        $this->emit('gotoTop');
    }

    public function render()
    {
        $teams = Team::livewireSearch($this->search);

        if ($this->all == false)
            $teams = $teams->whereBetween($this->byDate, [$this->fromDate . ' 00:00:00', $this->toDate . ' 23:59:59']);




        if ($this->admin_view_status == 'deleted')
            $teams = $teams->onlyTrashed();


        $teams = $teams->orderBy($this->orderBy, $this->orderWay)
            ->paginate($this->perPage);

        return view('livewire.team.team-index', compact('teams'));
    }
}
