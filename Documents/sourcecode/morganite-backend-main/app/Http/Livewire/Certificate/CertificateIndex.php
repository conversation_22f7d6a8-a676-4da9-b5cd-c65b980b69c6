<?php

namespace App\Http\Livewire\Certificate;

use App\Http\Controllers\HomeController;
use App\Models\Certificate;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Route;
use Livewire\Component;
use Livewire\WithFileUploads;
use Livewire\WithPagination;

class CertificateIndex extends Component
{
    use WithPagination;
    use WithFileUploads;

    protected $paginationTheme = 'bootstrap';

    public $perPage = 25;
    public $search = '';
    public $orderBy = 'id';
    public $orderWay = 'desc';
    public $showColumn;

    public $all;
    public $fromDate = null;
    public $toDate = null;
    public $byDate = 'created_at';

    public $selectedCertificates = [];

    public Certificate $certificate;
    private $certificates;
    public $user;





    public $url;
    public $admin_view_status = '';
    public function mount($admin_view_status = '')
    {
        $this->url = Route::current()->getName();
        $this->admin_view_status = $admin_view_status;
        $this->user = Auth::user();

        $this->certificate = new Certificate();

        $this->all = true;
        // $this->fromDate = date('Y-m-d', strtotime("-5 days"));
        $this->toDate = date('Y-m-d');




        $this->showColumn = collect([
            'id' => false,
            'slug' => false,


            'the_title' => true,
            'year' => true,
            'image' => true,
            'pdf' => true,

            // 'status' => false,
            'date' => true,
            'time' => false,
        ]);
    }

    public $slug;
    public $certificate_id, $the_title, $year, $image, $pdf;
    public $updateMode = false;

    private function resetInputFields()
    {
        $this->slug = '';

        $this->the_title = '';
        $this->year = '';
        $this->image = '';
        $this->pdf = '';
    }


    public function rules()
    {
        return [
            // 'patient_id' => "required|unique:patient_studies,patient_id,$this->patientstudy_id,id,study_id,$this->study_id,deleted_at,NULL",
            // 'slug' => $this-slug,


            'the_title' => 'required',
            'year' => 'required',
            'image' => 'required',
            'pdf' => 'required',
        ];
    }

    public function updated($propertyName)
    {
        $this->validateOnly($propertyName);
    }

    public function store()
    {
        $validatedData = $this->validate();

        Certificate::create([
            'slug' => $this->slug,

            'the_title' => $this->the_title,
            'year' => $this->year,
            'image' => HomeController::saveImageWeb($this->image, 'certificate'),
            'pdf' => HomeController::saveImageWeb($this->pdf, 'certificate'),
        ]);

        session()->flash('message', 'Certificate Created Successfully.');

        $this->resetInputFields();

        // $this->emit('close-model'); // Close model to using to jquery
    }

    public function edit($id)
    {
        $this->updateMode = true;
        $certificate = Certificate::where('id', $id)->first();
        $this->certificate_id = $id;
        $this->slug = $certificate->slug;


        $this->the_title = $certificate->the_title;
        $this->year = $certificate->year;
        // $this->image = $certificate->image;
        // $this->pdf = $certificate->pdf;
    }

    public function cancel()
    {
        $this->updateMode = false;
        $this->resetInputFields();
    }

    public function update()
    {

        if ($this->certificate_id) {
            $certificate = Certificate::find($this->certificate_id);
            $certificate->update([
                'slug' => $this->slug,

                'the_title' => $this->the_title,
                'year' => $this->year,
                'image' => $this->image ? HomeController::saveImageWeb($this->image, 'certificate') : $certificate->image,
                'pdf' => $this->pdf ? HomeController::saveImageWeb($this->pdf, 'certificate') : $certificate->pdf,
            ]);

            $this->updateMode = false;
            session()->flash('message', 'Certificate Updated Successfully.');
            $this->resetInputFields();
        }

        $this->emit('close-model'); // Close model to using to jquery
    }

    public function delete($id)
    {
        if ($id) {
            $certificate = Certificate::find($id);

            $certificate->delete();

            session()->flash('message', 'Certificate Deleted Successfully.');
        }
    }

    public function restore($id)
    {
        if ($id) {
            $certificate = Certificate::withTrashed()->find($id);

            $certificate->restore();

            session()->flash('message', 'Certificate Recovered Successfully.');
        }
    }

    public function updatedFromDate($fromDate)
    {
        $this->all = false;
    }

    public function updatedToDate($toDate)
    {
        $this->all = false;
    }

    public function clearFilter()
    {
        $this->all = true;
        // $this->fromDate = date('Y-m-d', strtotime("-5 days"));
        $this->byDate = 'created_at';
        $this->fromDate = '';
        $this->toDate = date('Y-m-d');
    }




    public function gotoPage($page)
    {
        $this->setPage($page);
        $this->emit('gotoTop');
    }

    public function nextPage()
    {
        $this->setPage($this->page + 1);
        $this->emit('gotoTop');
    }

    public function previousPage()
    {
        $this->setPage(max($this->page - 1, 1));
        $this->emit('gotoTop');
    }

    public function render()
    {
        $certificates = Certificate::livewireSearch($this->search);

        if ($this->all == false)
            $certificates = $certificates->whereBetween($this->byDate, [$this->fromDate . ' 00:00:00', $this->toDate . ' 23:59:59']);




        if ($this->admin_view_status == 'deleted')
            $certificates = $certificates->onlyTrashed();


        $certificates = $certificates->orderBy($this->orderBy, $this->orderWay)
            ->paginate($this->perPage);

        return view('livewire.certificate.certificate-index', compact('certificates'));
    }
}
