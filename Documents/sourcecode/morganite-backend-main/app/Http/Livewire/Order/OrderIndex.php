<?php

namespace App\Http\Livewire\Order;

use App\Models\Area;
use App\Models\City;
use App\Models\Country;
use App\Models\Order;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Route;
use Livewire\Component;
use Livewire\WithPagination;

class OrderIndex extends Component
{
    use WithPagination;

    protected $paginationTheme = 'bootstrap';

    public $perPage = 25;
    public $search = '';
    public $orderBy = 'id';
    public $orderWay = 'desc';
    public $showColumn;

    public $all;
    public $fromDate = null;
    public $toDate = null;
    public $byDate = 'created_at';

    public $selectedOrders = [];

    public Order $order;
    private $orders;
    public $user;


    public $users = [];

    public $order_statuses = [];

    public $addresses = [];

    public $promo_codes = [];

    public $countries = [];

    public $cites = [];

    public $areas = [];



    public $filter_users_id = [];

    public $filter_order_statuses_id = [];

    public $filter_addresses_id = [];

    public $filter_promo_codes_id = [];

    public $filter_country_id = [];

    public $filter_city_id = [];

    public $filter_area_id = [];


    public $url;
    public $admin_view_status = '';
    public function mount($admin_view_status = '')
    {
        $this->url = Route::current()->getName();
        $this->admin_view_status = $admin_view_status;
        $this->user = Auth::user();

        $this->order = new Order();

        $this->all = true;
        // $this->fromDate = date('Y-m-d', strtotime("-5 days"));
        $this->toDate = date('Y-m-d');



        $this->users = \App\Models\User::orderBy('id')->get();

        $this->order_statuses = \App\Models\OrderStatus::where('show', 1)->orderBy('sort')->get();

        // $this->addresses = \App\Models\Address::where('show', 1)->orderBy('sort')->get();

        $this->promo_codes = \App\Models\PromoCode::where('show', 1)->orderBy('sort')->get();

        $this->countries = Country::where('show', 1)->orderBy('sort')->get();

        $this->cites = City::where('show', 1)->orderBy('sort')->get();

        $this->areas = Area::where('show', 1)->orderBy('sort')->get();



        $this->showColumn = collect([
            'id' => false,
            'slug' => false,


            'user_id' => true,
            'order_status_id' => true,
            'total_price' => false,
            'delivery_date' => false,
            'delivery_status' => false,
            'delivery_fee' => false,
            'payment_method' => false,
            'payment_status' => false,
            'address_id' => false,
            'promo_code_id' => false,
            'discount_type' => false,
            'discount_amount' => false,
            'final_price' => true,

            // 'status' => false,
            'date' => true,
            'time' => false,
        ]);
    }

    public $slug;
    public $order_id, $user_id, $order_status_id, $total_price, $delivery_date, $delivery_status, $delivery_fee, $payment_method, $payment_status, $address_id, $promo_code_id, $discount_type, $discount_amount, $final_price;
    public $updateMode = false;

    private function resetInputFields()
    {
        $this->slug = '';

        $this->user_id = null;
        $this->order_status_id = null;
        $this->total_price = '';
        $this->delivery_date = '';
        $this->delivery_status = '';
        $this->delivery_fee = '';
        $this->payment_method = '';
        $this->payment_status = '';
        $this->address_id = null;
        $this->promo_code_id = null;
        $this->discount_type = '';
        $this->discount_amount = '';
        $this->final_price = '';
    }


    public function rules()
    {
        return [
            // 'patient_id' => "required|unique:patient_studies,patient_id,$this->patientstudy_id,id,study_id,$this->study_id,deleted_at,NULL",
            // 'slug' => $this-slug,


            'user_id' => 'required',
            'order_status_id' => 'required',
            'total_price' => 'required',
            'delivery_date' => 'required',
            'delivery_status' => 'required',
            'delivery_fee' => 'required',
            'payment_method' => 'required',
            'payment_status' => 'required',
            'address_id' => 'required',
            'promo_code_id' => 'required',
            'discount_type' => 'required',
            'discount_amount' => 'required',
            'final_price' => 'required',
        ];
    }

    public function updated($propertyName)
    {
        $this->validateOnly($propertyName);
    }

    public function store()
    {
        $validatedData = $this->validate();

        Order::create([
            'slug' => $this->slug,

            'user_id' => $this->user_id,
            'order_status_id' => $this->order_status_id,
            'total_price' => $this->total_price,
            'delivery_date' => $this->delivery_date,
            'delivery_status' => $this->delivery_status,
            'delivery_fee' => $this->delivery_fee,
            'payment_method' => $this->payment_method,
            'payment_status' => $this->payment_status,
            'address_id' => $this->address_id,
            'promo_code_id' => $this->promo_code_id,
            'discount_type' => $this->discount_type,
            'discount_amount' => $this->discount_amount,
            'final_price' => $this->final_price,
        ]);

        session()->flash('message', 'Order Created Successfully.');

        $this->resetInputFields();

        // $this->emit('close-model'); // Close model to using to jquery
    }

    public function edit($id)
    {
        $this->updateMode = true;
        $order = Order::where('id', $id)->first();
        $this->order_id = $id;
        $this->slug = $order->slug;


        $this->user_id = $order->user_id;
        $this->order_status_id = $order->order_status_id;
        $this->total_price = $order->total_price;
        $this->delivery_date = $order->delivery_date;
        $this->delivery_status = $order->delivery_status;
        $this->delivery_fee = $order->delivery_fee;
        $this->payment_method = $order->payment_method;
        $this->payment_status = $order->payment_status;
        $this->address_id = $order->address_id;
        $this->promo_code_id = $order->promo_code_id;
        $this->discount_type = $order->discount_type;
        $this->discount_amount = $order->discount_amount;
        $this->final_price = $order->final_price;
    }

    public function cancel()
    {
        $this->updateMode = false;
        $this->resetInputFields();
    }

    public function update()
    {

        if ($this->order_id) {
            $order = Order::find($this->order_id);
            $order->update([
                'slug' => $this->slug,

                'user_id' => $this->user_id,
                'order_status_id' => $this->order_status_id,
                'total_price' => $this->total_price,
                'delivery_date' => $this->delivery_date,
                'delivery_status' => $this->delivery_status,
                'delivery_fee' => $this->delivery_fee,
                'payment_method' => $this->payment_method,
                'payment_status' => $this->payment_status,
                'address_id' => $this->address_id,
                'promo_code_id' => $this->promo_code_id,
                'discount_type' => $this->discount_type,
                'discount_amount' => $this->discount_amount,
                'final_price' => $this->final_price,
            ]);

            $this->updateMode = false;
            session()->flash('message', 'Order Updated Successfully.');
            $this->resetInputFields();
        }

        $this->emit('close-model'); // Close model to using to jquery
    }

    public function delete($id)
    {
        if ($id) {
            $order = Order::find($id);

            $order->delete();

            session()->flash('message', 'Order Deleted Successfully.');
        }
    }

    public function restore($id)
    {
        if ($id) {
            $order = Order::withTrashed()->find($id);

            $order->restore();

            session()->flash('message', 'Order Recovered Successfully.');
        }
    }

    public function updatedFromDate($fromDate)
    {
        $this->all = false;
    }

    public function updatedToDate($toDate)
    {
        $this->all = false;
    }

    public function clearFilter()
    {
        $this->all = true;
        // $this->fromDate = date('Y-m-d', strtotime("-5 days"));
        $this->byDate = 'created_at';
        $this->fromDate = '';
        $this->toDate = date('Y-m-d');


        $this->filter_users_id = [];

        $this->filter_order_statuses_id = [];

        $this->filter_addresses_id = [];

        $this->filter_promo_codes_id = [];

        $this->filter_country_id = [];

        $this->filter_city_id = [];

        $this->filter_area_id = [];
    }


    public $select_user;
    public function updatedSelectUser($val)
    {
        $this->filter_users_id[] = $val;
    }


    public $select_order_status;
    public function updatedSelectOrderStatus($val)
    {
        $this->filter_order_statuses_id[] = $val;
    }


    public $select_address;
    public function updatedSelectAddress($val)
    {
        $this->filter_addresses_id[] = $val;
    }


    public $select_promo_code;
    public function updatedSelectPromoCode($val)
    {
        $this->filter_promo_codes_id[] = $val;
    }

    public $select_country;
    public function updatedSelectCountry($val)
    {
        $this->filter_country_id[] = $val;
    }
    public $select_city;
    public function updatedSelectCity($val)
    {

        $this->filter_city_id[] = $val;
    }

    public $select_area;
    public function updatedSelectArea($val)
    {
        $this->filter_area_id[] = $val;
    }




    public function gotoPage($page)
    {
        $this->setPage($page);
        $this->emit('gotoTop');
    }

    public function nextPage()
    {
        $this->setPage($this->page + 1);
        $this->emit('gotoTop');
    }

    public function previousPage()
    {
        $this->setPage(max($this->page - 1, 1));
        $this->emit('gotoTop');
    }

    public function acceptOrder($id)
    {
        $order = Order::find($id);
        $order->update(
            [
                'order_status_id' => 3
            ]
        );



        return redirect()->with('message', ' order ' . $order->id . ' accepted ');
    }



    public function rejectOrder($id)
    {
        $order = Order::find($id);

        $order->update(
            [
                'order_status_id' => 4
            ]
        );
        return redirect()->with('error', 'order reject');
    }

    public function setOrderStatus($order_id, $new_status)
    {
        $order = Order::find($order_id);

        $order->update([
            'order_status_id' => $new_status,
        ]);

        // 3 - Accept
        // 4 - Reject

        if (in_array($new_status, [3, 4])) {
            foreach ($order->items as $item) {
                $item->update(['order_status_id' => $new_status]);
            }
        }

        session()->flash('message', 'Order Status Updated Successfully.');
    }

    public function render()
    {
        $orders = Order::livewireSearch($this->search);

        if ($this->all == false)
            $orders = $orders->whereBetween($this->byDate, [$this->fromDate . ' 00:00:00', $this->toDate . ' 23:59:59']);



        if ($this->filter_users_id)
            $orders = $orders->whereIn('user_id', $this->filter_users_id);

        if ($this->filter_order_statuses_id)
            $orders = $orders->whereIn('order_status_id', $this->filter_order_statuses_id);

        if ($this->filter_addresses_id)
            $orders = $orders->whereIn('address_id', $this->filter_addresses_id);

        if ($this->filter_promo_codes_id)
            $orders = $orders->whereIn('promo_code_id', $this->filter_promo_codes_id);

        if ($this->filter_country_id) {
            $orders = $orders->whereHas('address', function ($q) {
                $q->whereIn('country_id', $this->filter_country_id);
            });
        }

        if ($this->filter_city_id) {
            $orders = $orders->whereHas('address', function ($q) {
                $q->whereIn('city_id', $this->filter_city_id);
            });
        }

        if ($this->admin_view_status == 'deleted')
            $orders = $orders->onlyTrashed();


        $orders = $orders->orderBy($this->orderBy, $this->orderWay)
            ->paginate($this->perPage);

        return view('livewire.order.order-index', compact('orders'));
    }
}
