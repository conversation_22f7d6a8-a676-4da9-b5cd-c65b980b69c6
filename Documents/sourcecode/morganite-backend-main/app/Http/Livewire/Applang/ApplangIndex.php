<?php

namespace App\Http\Livewire\Applang;

use App\Models\Applang;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Route;
use Livewire\Component;
use Livewire\WithPagination;

class ApplangIndex extends Component
{
    use WithPagination;

    protected $paginationTheme = 'bootstrap';

    public $perPage = 25;
    public $search = '';
    public $orderBy = 'id';
    public $orderWay = 'desc';
    public $showColumn;

    public $all;
    public $fromDate = null;
    public $toDate = null;
    public $byDate = 'created_at';

    public $selectedApplangs = [];

    public Applang $applang;
    private $applangs;
    public $user;





    public $url;
    public $admin_view_status = '';
    public function mount($admin_view_status = '')
    {
        $this->url = Route::current()->getName();
        $this->admin_view_status = $admin_view_status;
        $this->user = Auth::user();

        $this->applang = new Applang();

        $this->all = true;
        // $this->fromDate = date('Y-m-d', strtotime("-5 days"));
        $this->toDate = date('Y-m-d');




        $this->showColumn = collect([
            'id' => false,
            'slug' => false,


            'full_name' => true,
            'short_name' => true,
            'icon' => false,
            'status' => false,

            // 'status' => false,
            'date' => true,
            'time' => false,
        ]);
    }

    public $slug;
    public $applang_id, $full_name, $short_name, $icon, $status;
    public $updateMode = false;

    private function resetInputFields()
    {
        $this->slug = '';

        $this->full_name = '';
        $this->short_name = '';
        $this->icon = '';
        $this->status = '';
    }


    public function rules()
    {
        return [
            // 'patient_id' => "required|unique:patient_studies,patient_id,$this->patientstudy_id,id,study_id,$this->study_id,deleted_at,NULL",
            // 'slug' => $this-slug,


            'full_name' => 'required',
            'short_name' => 'required',
            'icon' => 'required',
            'status' => 'required',
        ];
    }

    public function updated($propertyName)
    {
        $this->validateOnly($propertyName);
    }

    public function store()
    {
        $validatedData = $this->validate();

        Applang::create([
            'slug' => $this->slug,

            'full_name' => $this->full_name,
            'short_name' => $this->short_name,
            'icon' => $this->icon,
            'status' => $this->status,
        ]);

        session()->flash('message', 'Applang Created Successfully.');

        $this->resetInputFields();

        // $this->emit('close-model'); // Close model to using to jquery
    }

    public function edit($id)
    {
        $this->updateMode = true;
        $applang = Applang::where('id', $id)->first();
        $this->applang_id = $id;
        $this->slug = $applang->slug;


        $this->full_name = $applang->full_name;
        $this->short_name = $applang->short_name;
        $this->icon = $applang->icon;
        $this->status = $applang->status;
    }

    public function cancel()
    {
        $this->updateMode = false;
        $this->resetInputFields();
    }

    public function update()
    {

        if ($this->applang_id) {
            $applang = Applang::find($this->applang_id);
            $applang->update([
                'slug' => $this->slug,

                'full_name' => $this->full_name,
                'short_name' => $this->short_name,
                'icon' => $this->icon,
                'status' => $this->status,
            ]);

            $this->updateMode = false;
            session()->flash('message', 'Applang Updated Successfully.');
            $this->resetInputFields();
        }

        $this->emit('close-model'); // Close model to using to jquery
    }

    public function delete($id)
    {
        if ($id) {
            $applang = Applang::find($id);

            $applang->delete();

            session()->flash('message', 'Applang Deleted Successfully.');
        }
    }

    public function restore($id)
    {
        if ($id) {
            $applang = Applang::withTrashed()->find($id);

            $applang->restore();

            session()->flash('message', 'Applang Recovered Successfully.');
        }
    }

    public function updatedFromDate($fromDate)
    {
        $this->all = false;
    }

    public function updatedToDate($toDate)
    {
        $this->all = false;
    }

    public function clearFilter()
    {
        $this->all = true;
        // $this->fromDate = date('Y-m-d', strtotime("-5 days"));
        $this->byDate = 'created_at';
        $this->fromDate = '';
        $this->toDate = date('Y-m-d');
    }




    public function gotoPage($page)
    {
        $this->setPage($page);
        $this->emit('gotoTop');
    }

    public function nextPage()
    {
        $this->setPage($this->page + 1);
        $this->emit('gotoTop');
    }

    public function previousPage()
    {
        $this->setPage(max($this->page - 1, 1));
        $this->emit('gotoTop');
    }

    public function render()
    {
        $applangs = Applang::livewireSearch($this->search);

        if ($this->all == false)
            $applangs = $applangs->whereBetween($this->byDate, [$this->fromDate . ' 00:00:00', $this->toDate . ' 23:59:59']);




        if ($this->admin_view_status == 'deleted')
            $applangs = $applangs->onlyTrashed();


        $applangs = $applangs->orderBy($this->orderBy, $this->orderWay)
            ->paginate($this->perPage);

        return view('livewire.applang.applang-index', compact('applangs'));
    }
}
