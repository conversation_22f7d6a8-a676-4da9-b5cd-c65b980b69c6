<?php

namespace App\Http\Livewire\Story;

use App\Http\Controllers\HomeController;
use App\Models\Appimage;
use App\Models\Story;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Route;
use Livewire\Component;
use Livewire\WithFileUploads;
use Livewire\WithPagination;

class StoryIndex extends Component
{
    use WithPagination;
    use WithFileUploads;

    protected $paginationTheme = 'bootstrap';

    public $perPage = 25;
    public $search = '';
    public $orderBy = 'id';
    public $orderWay = 'desc';
    public $showColumn;

    public $all;
    public $fromDate = null;
    public $toDate = null;
    public $byDate = 'created_at';

    public $selectedStories = [];

    public Story $story;
    private $stories;
    public $user;

    public $images = [];





    public $url;
    public $admin_view_status = '';
    public function mount($admin_view_status = '')
    {
        $this->url = Route::current()->getName();
        $this->admin_view_status = $admin_view_status;
        $this->user = Auth::user();

        $this->story = new Story();

        $this->all = true;
        // $this->fromDate = date('Y-m-d', strtotime("-5 days"));
        $this->toDate = date('Y-m-d');




        $this->showColumn = collect([
            'id' => false,
            'slug' => false,


            'the_title' => true,
            'the_description' => false,
            'year' => true,

            // 'status' => false,
            'date' => true,
            'time' => false,
        ]);
    }

    public $slug;
    public $story_id, $the_title, $the_description, $year;
    public $updateMode = false;

    private function resetInputFields()
    {
        $this->slug = '';

        $this->the_title = '';
        $this->the_description = '';
        $this->year = '';

        $this->images = [];
    }


    public function rules()
    {
        return [
            // 'patient_id' => "required|unique:patient_studies,patient_id,$this->patientstudy_id,id,study_id,$this->study_id,deleted_at,NULL",
            // 'slug' => $this-slug,


            'the_title' => 'required',
            // 'the_description' => 'required',
            'year' => 'required',
        ];
    }

    public function updated($propertyName)
    {
        $this->validateOnly($propertyName);
    }

    public function store()
    {
        $validatedData = $this->validate();

        $story = Story::create([
            'slug' => $this->slug,

            'the_title' => $this->the_title,
            'the_description' => $this->the_description,
            'year' => $this->year,
        ]);

        foreach ($this->images as $image) {
            Appimage::create([
                // 'slug' => $this->slug,
                'the_model_name' => 'story',
                'the_model_id' => $story->id,
                // 'place' => $this->place,
                'image' => HomeController::saveImageWeb($image, 'appimage'),
            ]);
        }

        session()->flash('message', 'Story Created Successfully.');

        $this->resetInputFields();

        // $this->emit('close-model'); // Close model to using to jquery
    }

    public function edit($id)
    {
        $this->updateMode = true;
        $story = Story::where('id', $id)->first();
        $this->story_id = $id;
        $this->slug = $story->slug;


        $this->the_title = $story->the_title;
        $this->the_description = $story->the_description;
        $this->year = $story->year;
    }

    public function cancel()
    {
        $this->updateMode = false;
        $this->resetInputFields();
    }

    public function update()
    {

        if ($this->story_id) {
            $story = Story::find($this->story_id);
            $story->update([
                'slug' => $this->slug,

                'the_title' => $this->the_title,
                'the_description' => $this->the_description,
                'year' => $this->year,
            ]);

            foreach ($this->images as $image) {
                Appimage::create([
                    // 'slug' => $this->slug,
                    'the_model_name' => 'story',
                    'the_model_id' => $story->id,
                    // 'place' => $this->place,
                    'image' => HomeController::saveImageWeb($image, 'appimage'),
                ]);
            }

            $this->updateMode = false;
            session()->flash('message', 'Story Updated Successfully.');
            $this->resetInputFields();
        }

        $this->emit('close-model'); // Close model to using to jquery
    }

    public function delete($id)
    {
        if ($id) {
            $story = Story::find($id);

            $story->delete();

            session()->flash('message', 'Story Deleted Successfully.');
        }
    }

    public function restore($id)
    {
        if ($id) {
            $story = Story::withTrashed()->find($id);

            $story->restore();

            session()->flash('message', 'Story Recovered Successfully.');
        }
    }

    public function updatedFromDate($fromDate)
    {
        $this->all = false;
    }

    public function updatedToDate($toDate)
    {
        $this->all = false;
    }

    public function clearFilter()
    {
        $this->all = true;
        // $this->fromDate = date('Y-m-d', strtotime("-5 days"));
        $this->byDate = 'created_at';
        $this->fromDate = '';
        $this->toDate = date('Y-m-d');
    }




    public function gotoPage($page)
    {
        $this->setPage($page);
        $this->emit('gotoTop');
    }

    public function nextPage()
    {
        $this->setPage($this->page + 1);
        $this->emit('gotoTop');
    }

    public function previousPage()
    {
        $this->setPage(max($this->page - 1, 1));
        $this->emit('gotoTop');
    }

    public function render()
    {
        $stories = Story::livewireSearch($this->search);

        if ($this->all == false)
            $stories = $stories->whereBetween($this->byDate, [$this->fromDate . ' 00:00:00', $this->toDate . ' 23:59:59']);




        if ($this->admin_view_status == 'deleted')
            $stories = $stories->onlyTrashed();


        $stories = $stories->orderBy($this->orderBy, $this->orderWay)
            ->paginate($this->perPage);

        return view('livewire.story.story-index', compact('stories'));
    }
}
