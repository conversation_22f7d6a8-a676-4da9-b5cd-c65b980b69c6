<?php

namespace App\Http\Livewire\Commint;

use App\Models\Commint;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Route;
use Livewire\Component;
use Livewire\WithPagination;

class CommintIndex extends Component
{
    use WithPagination;

    protected $paginationTheme = 'bootstrap';

    public $perPage = 25;
    public $search = '';
    public $orderBy = 'id';
    public $orderWay = 'desc';
    public $showColumn;

    public $all;
    public $fromDate = null;
    public $toDate = null;
    public $byDate = 'created_at';

    public $selectedCommints = [];

    public Commint $commint;
    private $commints;
    public $user;


    public $users = [];



    public $filter_users_id = [];


    public $url;
    public $admin_view_status = '';
    public function mount($admin_view_status = '')
    {
        $this->url = Route::current()->getName();
        $this->admin_view_status = $admin_view_status;
        $this->user = Auth::user();

        $this->commint = new Commint();

        $this->all = true;
        // $this->fromDate = date('Y-m-d', strtotime("-5 days"));
        $this->toDate = date('Y-m-d');



        $this->users = \App\Models\User::orderBy('id')->get();


        $this->showColumn = collect([
            'id' => false,
            'slug' => false,


            'user_id' => true,
            'name' => true,
            'email' => true,
            'content' => true,
            'the_model_name' => false,
            'the_model_id' => false,

            // 'status' => false,
            'date' => true,
            'time' => false,
        ]);
    }

    public $slug;
    public $commint_id, $user_id, $name, $email, $content, $the_model_name, $the_model_id;
    public $updateMode = false;

    private function resetInputFields()
    {
        $this->slug = '';

        $this->user_id = null;
        $this->name = '';
        $this->email = '';
        $this->content = '';
        $this->the_model_name = '';
        $this->the_model_id = null;
    }


    public function rules()
    {
        return [
            // 'patient_id' => "required|unique:patient_studies,patient_id,$this->patientstudy_id,id,study_id,$this->study_id,deleted_at,NULL",
            // 'slug' => $this-slug,


            'user_id' => 'required',
            'name' => 'required',
            'email' => 'required',
            'content' => 'required',
            'the_model_name' => 'required',
            'the_model_id' => 'required',
        ];
    }

    public function updated($propertyName)
    {
        $this->validateOnly($propertyName);
    }

    public function store()
    {
        $validatedData = $this->validate();

        Commint::create([
            'slug' => $this->slug,

            'user_id' => $this->user_id,
            'name' => $this->name,
            'email' => $this->email,
            'content' => $this->content,
            'the_model_name' => $this->the_model_name,
            'the_model_id' => $this->the_model_id,
        ]);

        session()->flash('message', 'Commint Created Successfully.');

        $this->resetInputFields();

        // $this->emit('close-model'); // Close model to using to jquery
    }

    public function edit($id)
    {
        $this->updateMode = true;
        $commint = Commint::where('id', $id)->first();
        $this->commint_id = $id;
        $this->slug = $commint->slug;


        $this->user_id = $commint->user_id;
        $this->name = $commint->name;
        $this->email = $commint->email;
        $this->content = $commint->content;
        $this->the_model_name = $commint->the_model_name;
        $this->the_model_id = $commint->the_model_id;
    }

    public function cancel()
    {
        $this->updateMode = false;
        $this->resetInputFields();
    }

    public function update()
    {

        if ($this->commint_id) {
            $commint = Commint::find($this->commint_id);
            $commint->update([
                'slug' => $this->slug,

                'user_id' => $this->user_id,
                'name' => $this->name,
                'email' => $this->email,
                'content' => $this->content,
                'the_model_name' => $this->the_model_name,
                'the_model_id' => $this->the_model_id,
            ]);

            $this->updateMode = false;
            session()->flash('message', 'Commint Updated Successfully.');
            $this->resetInputFields();
        }

        $this->emit('close-model'); // Close model to using to jquery
    }

    public function delete($id)
    {
        if ($id) {
            $commint = Commint::find($id);

            $commint->delete();

            session()->flash('message', 'Commint Deleted Successfully.');
        }
    }

    public function restore($id)
    {
        if ($id) {
            $commint = Commint::withTrashed()->find($id);

            $commint->restore();

            session()->flash('message', 'Commint Recovered Successfully.');
        }
    }

    public function updatedFromDate($fromDate)
    {
        $this->all = false;
    }

    public function updatedToDate($toDate)
    {
        $this->all = false;
    }

    public function clearFilter()
    {
        $this->all = true;
        // $this->fromDate = date('Y-m-d', strtotime("-5 days"));
        $this->byDate = 'created_at';
        $this->fromDate = '';
        $this->toDate = date('Y-m-d');


        $this->filter_users_id = [];
    }


    public $select_user;
    public function updatedSelectUser($val)
    {
        $this->filter_users_id[] = $val;
    }




    public function gotoPage($page)
    {
        $this->setPage($page);
        $this->emit('gotoTop');
    }

    public function nextPage()
    {
        $this->setPage($this->page + 1);
        $this->emit('gotoTop');
    }

    public function previousPage()
    {
        $this->setPage(max($this->page - 1, 1));
        $this->emit('gotoTop');
    }

    public function render()
    {
        $commints = Commint::livewireSearch($this->search);

        if ($this->all == false)
            $commints = $commints->whereBetween($this->byDate, [$this->fromDate . ' 00:00:00', $this->toDate . ' 23:59:59']);



        if ($this->filter_users_id)
            $commints = $commints->whereIn('user_id', $this->filter_users_id);


        if ($this->admin_view_status == 'deleted')
            $commints = $commints->onlyTrashed();


        $commints = $commints->orderBy($this->orderBy, $this->orderWay)
            ->paginate($this->perPage);

        return view('livewire.commint.commint-index', compact('commints'));
    }
}
