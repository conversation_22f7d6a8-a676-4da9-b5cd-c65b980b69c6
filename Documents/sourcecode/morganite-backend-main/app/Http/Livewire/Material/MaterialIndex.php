<?php

namespace App\Http\Livewire\Material;

use App\Http\Controllers\HomeController;
use App\Models\Appimage;
use App\Models\Material;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Route;
use Livewire\Component;
use Livewire\WithFileUploads;
use Livewire\WithPagination;

class MaterialIndex extends Component
{
    use WithPagination;
    use WithFileUploads;

    protected $paginationTheme = 'bootstrap';

    public $perPage = 25;
    public $search = '';
    public $orderBy = 'id';
    public $orderWay = 'desc';
    public $showColumn;

    public $all;
    public $fromDate = null;
    public $toDate = null;
    public $byDate = 'created_at';

    public $selectedMaterials = [];

    public Material $material;
    private $materials;
    public $user;


    public $parents = [];



    public $filter_parents_id = [];


    public $url;
    public $admin_view_status = '';

    public $images = [];


    public function mount($admin_view_status = '',)
    {
        $this->url = Route::current()->getName();
        $this->admin_view_status = $admin_view_status;
        $this->user = Auth::user();

        $this->material = new Material();

        $this->all = true;
        // $this->fromDate = date('Y-m-d', strtotime("-5 days"));
        $this->toDate = date('Y-m-d');



        $this->parents = \App\Models\Material::whereNullOrEmptyOrZero('parent_id')
            ->where('show', 1)->orderBy('sort')->get();


        $this->showColumn = collect([
            'id' => false,
            'slug' => false,


            'the_title' => true,
            'the_description' => false,
            'parent_id' => true,

            // 'status' => false,
            'date' => true,
            'time' => false,
        ]);
    }

    public $slug;
    public $material_id, $the_title, $the_description, $parent_id;
    public $updateMode = false;

    private function resetInputFields()
    {
        $this->slug = '';

        $this->the_title = '';
        $this->the_description = '';
        $this->parent_id = null;

        $this->images = [];
    }


    public function rules()
    {
        return [
            // 'patient_id' => "required|unique:patient_studies,patient_id,$this->patientstudy_id,id,study_id,$this->study_id,deleted_at,NULL",
            // 'slug' => $this-slug,


            'the_title' => 'required',
            // 'the_description' => 'required',
            // 'parent_id' => 'required',
        ];
    }

    public function updated($propertyName)
    {
        $this->validateOnly($propertyName);
    }

    public function store()
    {
        $validatedData = $this->validate();

        $material = Material::create([
            'slug' => $this->slug,

            'the_title' => $this->the_title,
            'the_description' => $this->the_description,
            'parent_id' => $this->parent_id,
        ]);

        foreach ($this->images as $image) {
            Appimage::create([
                // 'slug' => $this->slug,
                'the_model_name' => 'material',
                'the_model_id' => $material->id,
                // 'place' => $this->place,
                'image' => HomeController::saveImageWeb($image, 'appimage'),
            ]);
        }



        session()->flash('message', 'Material Created Successfully.');

        $this->resetInputFields();

        // $this->emit('close-model'); // Close model to using to jquery
    }

    public function edit($id)
    {
        $this->updateMode = true;
        $material = Material::where('id', $id)->first();
        $this->material_id = $id;
        $this->slug = $material->slug;


        $this->the_title = $material->the_title;
        $this->the_description = $material->the_description;
        $this->parent_id = $material->parent_id;
    }

    public function cancel()
    {
        $this->updateMode = false;
        $this->resetInputFields();
    }

    public function update()
    {

        if ($this->material_id) {
            $material = Material::find($this->material_id);
            $material->update([
                'slug' => $this->slug,

                'the_title' => $this->the_title,
                'the_description' => $this->the_description,
                'parent_id' => $this->parent_id,
            ]);



            $this->updateMode = false;
            session()->flash('message', 'Material Updated Successfully.');
            $this->resetInputFields();
        }

        $this->emit('close-model'); // Close model to using to jquery
    }

    public function delete($id)
    {
        if ($id) {
            $material = Material::find($id);

            $material->delete();

            session()->flash('message', 'Material Deleted Successfully.');
        }
    }

    public function restore($id)
    {
        if ($id) {
            $material = Material::withTrashed()->find($id);

            $material->restore();

            session()->flash('message', 'Material Recovered Successfully.');
        }
    }

    public function updatedFromDate($fromDate)
    {
        $this->all = false;
    }

    public function updatedToDate($toDate)
    {
        $this->all = false;
    }

    public function clearFilter()
    {
        $this->all = true;
        // $this->fromDate = date('Y-m-d', strtotime("-5 days"));
        $this->byDate = 'created_at';
        $this->fromDate = '';
        $this->toDate = date('Y-m-d');


        $this->filter_parents_id = [];
    }


    public $select_parent;
    public function updatedSelectParent($val)
    {
        $this->filter_parents_id[] = $val;
    }




    public function gotoPage($page)
    {
        $this->setPage($page);
        $this->emit('gotoTop');
    }

    public function nextPage()
    {
        $this->setPage($this->page + 1);
        $this->emit('gotoTop');
    }

    public function previousPage()
    {
        $this->setPage(max($this->page - 1, 1));
        $this->emit('gotoTop');
    }

    public function render()
    {
        $materials = Material::livewireSearch($this->search);

        if ($this->all == false)
            $materials = $materials->whereBetween($this->byDate, [$this->fromDate . ' 00:00:00', $this->toDate . ' 23:59:59']);



        if ($this->filter_parents_id)
            $materials = $materials->whereIn('parent_id', $this->filter_parents_id);


        if ($this->admin_view_status == 'deleted')
            $materials = $materials->onlyTrashed();


        $materials = $materials->orderBy($this->orderBy, $this->orderWay)
            ->paginate($this->perPage);

        return view('livewire.material.material-index', compact('materials'));
    }
}
