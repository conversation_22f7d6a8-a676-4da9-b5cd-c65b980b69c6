<?php

namespace App\Http\Livewire\Tag;

use App\Models\Tag;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Route;
use Livewire\Component;
use Livewire\WithPagination;

class TagIndex extends Component
{
    use WithPagination;

    protected $paginationTheme = 'bootstrap';

    public $perPage = 25;
    public $search = '';
    public $orderBy = 'id';
    public $orderWay = 'desc';
    public $showColumn;

    public $all;
    public $fromDate = null;
    public $toDate = null;
    public $byDate = 'created_at';

    public $selectedTags = [];

    public Tag $tag;
    private $tags;
    public $user;





    public $url;
    public $admin_view_status = '';
    public function mount($admin_view_status = '')
    {
        $this->url = Route::current()->getName();
        $this->admin_view_status = $admin_view_status;
        $this->user = Auth::user();

        $this->tag = new Tag();

        $this->all = true;
        // $this->fromDate = date('Y-m-d', strtotime("-5 days"));
        $this->toDate = date('Y-m-d');




        $this->showColumn = collect([
            'id' => false,
            'slug' => false,


            'the_name' => true,

            // 'status' => false,
            'date' => true,
            'time' => false,
        ]);
    }

    public $slug;
    public $tag_id, $the_name;
    public $updateMode = false;

    private function resetInputFields()
    {
        $this->slug = '';

        $this->the_name = '';
    }


    public function rules()
    {
        return [
            // 'patient_id' => "required|unique:patient_studies,patient_id,$this->patientstudy_id,id,study_id,$this->study_id,deleted_at,NULL",
            // 'slug' => $this-slug,


            'the_name' => 'required',
        ];
    }

    public function updated($propertyName)
    {
        $this->validateOnly($propertyName);
    }

    public function store()
    {
        $validatedData = $this->validate();

        Tag::create([
            'slug' => $this->slug,

            'the_name' => $this->the_name,
        ]);

        session()->flash('message', 'Tag Created Successfully.');

        $this->resetInputFields();

        // $this->emit('close-model'); // Close model to using to jquery
    }

    public function edit($id)
    {
        $this->updateMode = true;
        $tag = Tag::where('id', $id)->first();
        $this->tag_id = $id;
        $this->slug = $tag->slug;


        $this->the_name = $tag->the_name;
    }

    public function cancel()
    {
        $this->updateMode = false;
        $this->resetInputFields();
    }

    public function update()
    {

        if ($this->tag_id) {
            $tag = Tag::find($this->tag_id);
            $tag->update([
                'slug' => $this->slug,

                'the_name' => $this->the_name,
            ]);

            $this->updateMode = false;
            session()->flash('message', 'Tag Updated Successfully.');
            $this->resetInputFields();
        }

        $this->emit('close-model'); // Close model to using to jquery
    }

    public function delete($id)
    {
        if ($id) {
            $tag = Tag::find($id);

            $tag->delete();

            session()->flash('message', 'Tag Deleted Successfully.');
        }
    }

    public function restore($id)
    {
        if ($id) {
            $tag = Tag::withTrashed()->find($id);

            $tag->restore();

            session()->flash('message', 'Tag Recovered Successfully.');
        }
    }

    public function updatedFromDate($fromDate)
    {
        $this->all = false;
    }

    public function updatedToDate($toDate)
    {
        $this->all = false;
    }

    public function clearFilter()
    {
        $this->all = true;
        // $this->fromDate = date('Y-m-d', strtotime("-5 days"));
        $this->byDate = 'created_at';
        $this->fromDate = '';
        $this->toDate = date('Y-m-d');
    }




    public function gotoPage($page)
    {
        $this->setPage($page);
        $this->emit('gotoTop');
    }

    public function nextPage()
    {
        $this->setPage($this->page + 1);
        $this->emit('gotoTop');
    }

    public function previousPage()
    {
        $this->setPage(max($this->page - 1, 1));
        $this->emit('gotoTop');
    }

    public function render()
    {
        $tags = Tag::livewireSearch($this->search);

        if ($this->all == false)
            $tags = $tags->whereBetween($this->byDate, [$this->fromDate . ' 00:00:00', $this->toDate . ' 23:59:59']);




        if ($this->admin_view_status == 'deleted')
            $tags = $tags->onlyTrashed();


        $tags = $tags->orderBy($this->orderBy, $this->orderWay)
            ->paginate($this->perPage);

        return view('livewire.tag.tag-index', compact('tags'));
    }
}
