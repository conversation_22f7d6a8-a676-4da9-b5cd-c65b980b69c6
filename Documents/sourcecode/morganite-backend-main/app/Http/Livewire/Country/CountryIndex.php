<?php

namespace App\Http\Livewire\Country;

use App\Models\Country;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Route;
use Livewire\Component;
use Livewire\WithPagination;

class CountryIndex extends Component
{
    use WithPagination;

    protected $paginationTheme = 'bootstrap';

    public $perPage = 25;
    public $search = '';
    public $orderBy = 'id';
    public $orderWay = 'desc';
    public $showColumn;

    public $all;
    public $fromDate = null;
    public $toDate = null;
    public $byDate = 'created_at';

    public $selectedCountries = [];

    public Country $country;
    private $countries;
    public $user;





    public $url;
    public $admin_view_status = '';
    public function mount($admin_view_status = '')
    {
        $this->url = Route::current()->getName();
        $this->admin_view_status = $admin_view_status;
        $this->user = Auth::user();

        $this->country = new Country();

        $this->all = true;
        // $this->fromDate = date('Y-m-d', strtotime("-5 days"));
        $this->toDate = date('Y-m-d');




        $this->showColumn = collect([
            'id' => false,
            'slug' => false,


            'the_name' => true,
            'status' => false,
            'delivery_fee' => true,

            // 'status' => false,
            'date' => true,
            'time' => false,
        ]);
    }

    public $slug;
    public $country_id, $the_name, $status, $delivery_fee;
    public $updateMode = false;

    private function resetInputFields()
    {
        $this->slug = '';

        $this->the_name = '';
        $this->status = '';
        $this->delivery_fee = '';
    }


    public function rules()
    {
        return [
            // 'patient_id' => "required|unique:patient_studies,patient_id,$this->patientstudy_id,id,study_id,$this->study_id,deleted_at,NULL",
            // 'slug' => $this-slug,


            'the_name' => 'required',
            'status' => 'required',
            'delivery_fee' => 'required',
        ];
    }

    public function updated($propertyName)
    {
        $this->validateOnly($propertyName);
    }

    public function store()
    {
        $validatedData = $this->validate();

        Country::create([
            'slug' => $this->slug,

            'the_name' => $this->the_name,
            'status' => $this->status,
            'delivery_fee' => $this->delivery_fee,
        ]);

        session()->flash('message', 'Country Created Successfully.');

        $this->resetInputFields();

        // $this->emit('close-model'); // Close model to using to jquery
    }

    public function edit($id)
    {
        $this->updateMode = true;
        $country = Country::where('id', $id)->first();
        $this->country_id = $id;
        $this->slug = $country->slug;


        $this->the_name = $country->the_name;
        $this->status = $country->status;
        $this->delivery_fee = $country->delivery_fee;
    }

    public function cancel()
    {
        $this->updateMode = false;
        $this->resetInputFields();
    }

    public function update()
    {

        if ($this->country_id) {
            $country = Country::find($this->country_id);
            $country->update([
                'slug' => $this->slug,

                'the_name' => $this->the_name,
                'status' => $this->status,
                'delivery_fee' => $this->delivery_fee,
            ]);

            $this->updateMode = false;
            session()->flash('message', 'Country Updated Successfully.');
            $this->resetInputFields();
        }

        $this->emit('close-model'); // Close model to using to jquery
    }

    public function delete($id)
    {
        if ($id) {
            $country = Country::find($id);

            $country->delete();

            session()->flash('message', 'Country Deleted Successfully.');
        }
    }

    public function restore($id)
    {
        if ($id) {
            $country = Country::withTrashed()->find($id);

            $country->restore();

            session()->flash('message', 'Country Recovered Successfully.');
        }
    }

    public function updatedFromDate($fromDate)
    {
        $this->all = false;
    }

    public function updatedToDate($toDate)
    {
        $this->all = false;
    }

    public function clearFilter()
    {
        $this->all = true;
        // $this->fromDate = date('Y-m-d', strtotime("-5 days"));
        $this->byDate = 'created_at';
        $this->fromDate = '';
        $this->toDate = date('Y-m-d');
    }




    public function gotoPage($page)
    {
        $this->setPage($page);
        $this->emit('gotoTop');
    }

    public function nextPage()
    {
        $this->setPage($this->page + 1);
        $this->emit('gotoTop');
    }

    public function previousPage()
    {
        $this->setPage(max($this->page - 1, 1));
        $this->emit('gotoTop');
    }

    public function render()
    {
        $countries = Country::livewireSearch($this->search);

        if ($this->all == false)
            $countries = $countries->whereBetween($this->byDate, [$this->fromDate . ' 00:00:00', $this->toDate . ' 23:59:59']);




        if ($this->admin_view_status == 'deleted')
            $countries = $countries->onlyTrashed();


        $countries = $countries->orderBy($this->orderBy, $this->orderWay)
            ->paginate($this->perPage);

        return view('livewire.country.country-index', compact('countries'));
    }
}
