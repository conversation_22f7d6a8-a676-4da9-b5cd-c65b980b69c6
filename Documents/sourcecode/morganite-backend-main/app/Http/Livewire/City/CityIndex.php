<?php

namespace App\Http\Livewire\City;

use App\Models\City;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Route;
use Livewire\Component;
use Livewire\WithPagination;

class CityIndex extends Component
{
    use WithPagination;

    protected $paginationTheme = 'bootstrap';

    public $perPage = 25;
    public $search = '';
    public $orderBy = 'id';
    public $orderWay = 'desc';
    public $showColumn;

    public $all;
    public $fromDate = null;
    public $toDate = null;
    public $byDate = 'created_at';

    public $selectedCities = [];

    public City $city;
    private $cities;
    public $user;


    public $countries = [];



    public $filter_countries_id = [];


    public $url;
    public $admin_view_status = '';
    public function mount($admin_view_status = '')
    {
        $this->url = Route::current()->getName();
        $this->admin_view_status = $admin_view_status;
        $this->user = Auth::user();

        $this->city = new City();

        $this->all = true;
        // $this->fromDate = date('Y-m-d', strtotime("-5 days"));
        $this->toDate = date('Y-m-d');



        $this->countries = \App\Models\Country::where('show', 1)->orderBy('sort')->get();


        $this->showColumn = collect([
            'id' => false,
            'slug' => false,


            'the_name' => true,
            'delivery_fee' => true,
            'status' => false,
            'country_id' => true,

            // 'status' => false,
            'date' => true,
            'time' => false,
        ]);
    }

    public $slug;
    public $city_id, $the_name, $delivery_fee, $status, $country_id;
    public $updateMode = false;

    private function resetInputFields()
    {
        $this->slug = '';

        $this->the_name = '';
        $this->delivery_fee = '';
        $this->status = '';
        $this->country_id = null;
    }


    public function rules()
    {
        return [
            // 'patient_id' => "required|unique:patient_studies,patient_id,$this->patientstudy_id,id,study_id,$this->study_id,deleted_at,NULL",
            // 'slug' => $this-slug,


            'the_name' => 'required',
            'delivery_fee' => 'required',
            'status' => 'required',
            'country_id' => 'required',
        ];
    }

    public function updated($propertyName)
    {
        $this->validateOnly($propertyName);
    }

    public function store()
    {
        $validatedData = $this->validate();

        City::create([
            'slug' => $this->slug,

            'the_name' => $this->the_name,
            'delivery_fee' => $this->delivery_fee,
            'status' => $this->status,
            'country_id' => $this->country_id,
        ]);

        session()->flash('message', 'City Created Successfully.');

        $this->resetInputFields();

        // $this->emit('close-model'); // Close model to using to jquery
    }

    public function edit($id)
    {
        $this->updateMode = true;
        $city = City::where('id', $id)->first();
        $this->city_id = $id;
        $this->slug = $city->slug;


        $this->the_name = $city->the_name;
        $this->delivery_fee = $city->delivery_fee;
        $this->status = $city->status;
        $this->country_id = $city->country_id;
    }

    public function cancel()
    {
        $this->updateMode = false;
        $this->resetInputFields();
    }

    public function update()
    {

        if ($this->city_id) {
            $city = City::find($this->city_id);
            $city->update([
                'slug' => $this->slug,

                'the_name' => $this->the_name,
                'delivery_fee' => $this->delivery_fee,
                'status' => $this->status,
                'country_id' => $this->country_id,
            ]);

            $this->updateMode = false;
            session()->flash('message', 'City Updated Successfully.');
            $this->resetInputFields();
        }

        $this->emit('close-model'); // Close model to using to jquery
    }

    public function delete($id)
    {
        if ($id) {
            $city = City::find($id);

            $city->delete();

            session()->flash('message', 'City Deleted Successfully.');
        }
    }

    public function restore($id)
    {
        if ($id) {
            $city = City::withTrashed()->find($id);

            $city->restore();

            session()->flash('message', 'City Recovered Successfully.');
        }
    }

    public function updatedFromDate($fromDate)
    {
        $this->all = false;
    }

    public function updatedToDate($toDate)
    {
        $this->all = false;
    }

    public function clearFilter()
    {
        $this->all = true;
        // $this->fromDate = date('Y-m-d', strtotime("-5 days"));
        $this->byDate = 'created_at';
        $this->fromDate = '';
        $this->toDate = date('Y-m-d');


        $this->filter_countries_id = [];
    }


    public $select_country;
    public function updatedSelectCountry($val)
    {
        $this->filter_countries_id[] = $val;
    }




    public function gotoPage($page)
    {
        $this->setPage($page);
        $this->emit('gotoTop');
    }

    public function nextPage()
    {
        $this->setPage($this->page + 1);
        $this->emit('gotoTop');
    }

    public function previousPage()
    {
        $this->setPage(max($this->page - 1, 1));
        $this->emit('gotoTop');
    }

    public function render()
    {
        $cities = City::livewireSearch($this->search);

        if ($this->all == false)
            $cities = $cities->whereBetween($this->byDate, [$this->fromDate . ' 00:00:00', $this->toDate . ' 23:59:59']);



        if ($this->filter_countries_id)
            $cities = $cities->whereIn('country_id', $this->filter_countries_id);


        if ($this->admin_view_status == 'deleted')
            $cities = $cities->onlyTrashed();


        $cities = $cities->orderBy($this->orderBy, $this->orderWay)
            ->paginate($this->perPage);

        return view('livewire.city.city-index', compact('cities'));
    }
}
