<?php

namespace App\Http\Livewire\Blog;

use App\Http\Controllers\HomeController;
use App\Models\Blog;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Route;
use Livewire\Component;
use Livewire\WithFileUploads;
use Livewire\WithPagination;

class BlogIndex extends Component
{
    use WithPagination;

    use WithFileUploads;

    protected $paginationTheme = 'bootstrap';

    public $perPage = 25;
    public $search = '';
    public $orderBy = 'id';
    public $orderWay = 'desc';
    public $showColumn;

    public $all;
    public $fromDate = null;
    public $toDate = null;
    public $byDate = 'created_at';

    public $selectedBlogs = [];

    public Blog $blog;
    private $blogs;
    public $user;


    public $authers = [];



    public $filter_authers_id = [];


    public $url;
    public $admin_view_status = '';
    public function mount($admin_view_status = '')
    {
        $this->url = Route::current()->getName();
        $this->admin_view_status = $admin_view_status;
        $this->user = Auth::user();

        $this->blog = new Blog();

        $this->all = true;
        // $this->fromDate = date('Y-m-d', strtotime("-5 days"));
        $this->toDate = date('Y-m-d');



        $this->authers = \App\Models\User::orderBy('id')->get();


        $this->showColumn = collect([
            'id' => false,
            'slug' => false,


            'the_title' => true,
            'the_content' => false,
            'thumbnail' => true,
            'auther_id' => true,
            'the_summary' => false,

            // 'status' => false,
            'date' => true,
            'time' => false,
        ]);
    }

    public $slug;
    public $blog_id, $the_title, $the_content, $thumbnail, $auther_id, $the_summary;
    public $updateMode = false;

    private function resetInputFields()
    {
        $this->slug = '';

        $this->the_title = '';
        $this->the_content = '';
        $this->thumbnail = '';
        $this->auther_id = null;
        $this->the_summary = '';
    }


    public function rules()
    {
        return [
            // 'patient_id' => "required|unique:patient_studies,patient_id,$this->patientstudy_id,id,study_id,$this->study_id,deleted_at,NULL",
            // 'slug' => $this-slug,


            'the_title' => 'required',
            'the_content' => 'required',
            'thumbnail' => 'required',
            'auther_id' => 'required',
            'the_summary' => 'required',
        ];
    }

    public function updated($propertyName)
    {
        $this->validateOnly($propertyName);
    }

    public function store()
    {
        $validatedData = $this->validate();

        Blog::create([
            'slug' => $this->slug,

            'the_title' => $this->the_title,
            'the_content' => $this->the_content,
            'thumbnail' => HomeController::saveImageWeb($this->thumbnail, 'blog'),
            'auther_id' => $this->auther_id,
            'the_summary' => $this->the_summary,
        ]);

        session()->flash('message', 'Blog Created Successfully.');

        $this->resetInputFields();

        // $this->emit('close-model'); // Close model to using to jquery
    }

    public function edit($id)
    {
        $this->updateMode = true;
        $blog = Blog::where('id', $id)->first();
        $this->blog_id = $id;
        $this->slug = $blog->slug;


        $this->the_title = $blog->the_title;
        $this->the_content = $blog->the_content;
        // $this->thumbnail = $blog->thumbnail;
        $this->auther_id = $blog->auther_id;
        $this->the_summary = $blog->the_summary;
    }

    public function cancel()
    {
        $this->updateMode = false;
        $this->resetInputFields();
    }

    public function update()
    {

        if ($this->blog_id) {
            $blog = Blog::find($this->blog_id);
            $blog->update([
                'slug' => $this->slug,

                'the_title' => $this->the_title,
                'the_content' => $this->the_content,
                'thumbnail' => $this->thumbnail ? HomeController::saveImageWeb($this->thumbnail, 'blog') : $blog->thumbnail,
                'auther_id' => $this->auther_id,
                'the_summary' => $this->the_summary,
            ]);

            $this->updateMode = false;
            session()->flash('message', 'Blog Updated Successfully.');
            $this->resetInputFields();
        }

        $this->emit('close-model'); // Close model to using to jquery
    }

    public function delete($id)
    {
        if ($id) {
            $blog = Blog::find($id);

            $blog->delete();

            session()->flash('message', 'Blog Deleted Successfully.');
        }
    }

    public function restore($id)
    {
        if ($id) {
            $blog = Blog::withTrashed()->find($id);

            $blog->restore();

            session()->flash('message', 'Blog Recovered Successfully.');
        }
    }

    public function updatedFromDate($fromDate)
    {
        $this->all = false;
    }

    public function updatedToDate($toDate)
    {
        $this->all = false;
    }

    public function clearFilter()
    {
        $this->all = true;
        // $this->fromDate = date('Y-m-d', strtotime("-5 days"));
        $this->byDate = 'created_at';
        $this->fromDate = '';
        $this->toDate = date('Y-m-d');


        $this->filter_authers_id = [];
    }


    public $select_auther;
    public function updatedSelectAuther($val)
    {
        $this->filter_authers_id[] = $val;
    }




    public function gotoPage($page)
    {
        $this->setPage($page);
        $this->emit('gotoTop');
    }

    public function nextPage()
    {
        $this->setPage($this->page + 1);
        $this->emit('gotoTop');
    }

    public function previousPage()
    {
        $this->setPage(max($this->page - 1, 1));
        $this->emit('gotoTop');
    }

    public function render()
    {
        $blogs = Blog::livewireSearch($this->search);

        if ($this->all == false)
            $blogs = $blogs->whereBetween($this->byDate, [$this->fromDate . ' 00:00:00', $this->toDate . ' 23:59:59']);



        if ($this->filter_authers_id)
            $blogs = $blogs->whereIn('auther_id', $this->filter_authers_id);


        if ($this->admin_view_status == 'deleted')
            $blogs = $blogs->onlyTrashed();


        $blogs = $blogs->orderBy($this->orderBy, $this->orderWay)
            ->paginate($this->perPage);

        return view('livewire.blog.blog-index', compact('blogs'));
    }
}
