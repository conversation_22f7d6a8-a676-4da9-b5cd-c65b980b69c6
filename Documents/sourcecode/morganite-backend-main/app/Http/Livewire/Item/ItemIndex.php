<?php

namespace App\Http\Livewire\Item;

use App\Models\Item;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Route;
use Livewire\Component;
use Livewire\WithPagination;

class ItemIndex extends Component
{
    use WithPagination;

    protected $paginationTheme = 'bootstrap';

    public $perPage = 25;
    public $search = '';
    public $orderBy = 'id';
    public $orderWay = 'desc';
    public $showColumn;

    public $all;
    public $fromDate = null;
    public $toDate = null;
    public $byDate = 'created_at';

    public $selectedItems = [];

    public Item $item;
    private $items;
    public $user;


    public $orders = [];

    public $products = [];

    public $attribute_values = [];



    public $filter_orders_id = [];

    public $filter_products_id = [];

    public $filter_attribute_values_id = [];

    public $order;

    public $url;
    public $admin_view_status = '';
    public function mount($admin_view_status = '', $order = null)
    {
        $this->url = Route::current()->getName();
        $this->admin_view_status = $admin_view_status;
        $this->user = Auth::user();

        $this->item = new Item();

        $this->all = true;
        // $this->fromDate = date('Y-m-d', strtotime("-5 days"));
        $this->toDate = date('Y-m-d');

        if ($order) {
            $this->order = $order;
        }


        // $this->orders = \App\Models\Order::where('show', 1)->orderBy('sort')->get();

        $this->products = \App\Models\Product::where('show', 1)->orderBy('sort')->get();

        // $this->attribute_values = \App\Models\AttributeValue::where('show', 1)->orderBy('sort')->get();


        $this->showColumn = collect([
            'id' => false,
            'slug' => false,


            'order_id' => false,
            'product_id' => true,
            'price' => true,
            'quantity' => true,
            'attribute_value_id' => false,
            'note' => true,

            // 'status' => false,
            'date' => false,
            'time' => false,
        ]);
    }

    public $slug;
    public $item_id, $order_id, $product_id, $price, $quantity, $attribute_value_id, $note;
    public $updateMode = false;

    private function resetInputFields()
    {
        $this->slug = '';

        $this->order_id = null;
        $this->product_id = null;
        $this->price = '';
        $this->quantity = '';
        $this->attribute_value_id = null;
        $this->note = '';
    }


    public function rules()
    {
        return [
            // 'patient_id' => "required|unique:patient_studies,patient_id,$this->patientstudy_id,id,study_id,$this->study_id,deleted_at,NULL",
            // 'slug' => $this-slug,


            'order_id' => 'required',
            'product_id' => 'required',
            'price' => 'required',
            'quantity' => 'required',
            'attribute_value_id' => 'required',
            'note' => 'required',
        ];
    }

    public function updated($propertyName)
    {
        $this->validateOnly($propertyName);
    }

    public function store()
    {
        $validatedData = $this->validate();

        Item::create([
            'slug' => $this->slug,

            'order_id' => $this->order_id,
            'product_id' => $this->product_id,
            'price' => $this->price,
            'quantity' => $this->quantity,
            'attribute_value_id' => $this->attribute_value_id,
            'note' => $this->note,
        ]);

        session()->flash('message', 'Item Created Successfully.');

        $this->resetInputFields();

        // $this->emit('close-model'); // Close model to using to jquery
    }

    public function edit($id)
    {
        $this->updateMode = true;
        $item = Item::where('id', $id)->first();
        $this->item_id = $id;
        $this->slug = $item->slug;


        $this->order_id = $item->order_id;
        $this->product_id = $item->product_id;
        $this->price = $item->price;
        $this->quantity = $item->quantity;
        $this->attribute_value_id = $item->attribute_value_id;
        $this->note = $item->note;
    }

    public function cancel()
    {
        $this->updateMode = false;
        $this->resetInputFields();
    }

    public function update()
    {

        if ($this->item_id) {
            $item = Item::find($this->item_id);
            $item->update([
                'slug' => $this->slug,

                'order_id' => $this->order_id,
                'product_id' => $this->product_id,
                'price' => $this->price,
                'quantity' => $this->quantity,
                'attribute_value_id' => $this->attribute_value_id,
                'note' => $this->note,
            ]);

            $this->updateMode = false;
            session()->flash('message', 'Item Updated Successfully.');
            $this->resetInputFields();
        }

        $this->emit('close-model'); // Close model to using to jquery
    }

    public function delete($id)
    {
        if ($id) {
            $item = Item::find($id);

            $item->delete();

            session()->flash('message', 'Item Deleted Successfully.');
        }
    }

    public function restore($id)
    {
        if ($id) {
            $item = Item::withTrashed()->find($id);

            $item->restore();

            session()->flash('message', 'Item Recovered Successfully.');
        }
    }

    public function updatedFromDate($fromDate)
    {
        $this->all = false;
    }

    public function updatedToDate($toDate)
    {
        $this->all = false;
    }

    public function clearFilter()
    {
        $this->all = true;
        // $this->fromDate = date('Y-m-d', strtotime("-5 days"));
        $this->byDate = 'created_at';
        $this->fromDate = '';
        $this->toDate = date('Y-m-d');


        $this->filter_orders_id = [];

        $this->filter_products_id = [];

        $this->filter_attribute_values_id = [];
    }


    public $select_order;
    public function updatedSelectOrder($val)
    {
        $this->filter_orders_id[] = $val;
    }


    public $select_product;
    public function updatedSelectProduct($val)
    {
        $this->filter_products_id[] = $val;
    }


    public $select_attribute_value;
    public function updatedSelectAttributeValue($val)
    {
        $this->filter_attribute_values_id[] = $val;
    }




    public function gotoPage($page)
    {
        $this->setPage($page);
        $this->emit('gotoTop');
    }

    public function nextPage()
    {
        $this->setPage($this->page + 1);
        $this->emit('gotoTop');
    }

    public function previousPage()
    {
        $this->setPage(max($this->page - 1, 1));
        $this->emit('gotoTop');
    }

    public function render()
    {
        $items = Item::livewireSearch($this->search);

        if ($this->all == false)
            $items = $items->whereBetween($this->byDate, [$this->fromDate . ' 00:00:00', $this->toDate . ' 23:59:59']);



        if ($this->filter_orders_id)
            $items = $items->whereIn('order_id', $this->filter_orders_id);

        if ($this->filter_products_id)
            $items = $items->whereIn('product_id', $this->filter_products_id);

        if ($this->filter_attribute_values_id)
            $items = $items->whereIn('attribute_value_id', $this->filter_attribute_values_id);


        if ($this->order)
            $items = $items->where('order_id', $this->order->id);


        if ($this->admin_view_status == 'deleted')
            $items = $items->onlyTrashed();


        $items = $items->orderBy($this->orderBy, $this->orderWay)
            ->paginate($this->perPage);

        return view('livewire.item.item-index', compact('items'));
    }
}
