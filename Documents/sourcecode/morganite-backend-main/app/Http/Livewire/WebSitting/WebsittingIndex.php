<?php

namespace App\Http\Livewire\Websitting;

use App\Models\WebSitting;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Route;
use Livewire\Component;
use Livewire\WithPagination;

class WebsittingIndex extends Component
{
    use WithPagination;

    protected $paginationTheme = 'bootstrap';

    public $perPage = 25;
    public $search = '';
    public $orderBy = 'id';
    public $orderWay = 'desc';
    public $showColumn;

    public $all;
    public $fromDate = null;
    public $toDate = null;
    public $byDate = 'created_at';

    public $selectedWebSittings = [];

    public WebSitting $websitting;
    private $websittings;
    public $user;





    public $url;
    public $admin_view_status = '';
    public function mount($admin_view_status = '')
    {
        $this->url = Route::current()->getName();
        $this->admin_view_status = $admin_view_status;
        $this->user = Auth::user();

        $this->websitting = new WebSitting();

        $this->all = true;
        // $this->fromDate = date('Y-m-d', strtotime("-5 days"));
        $this->toDate = date('Y-m-d');




        $this->showColumn = collect([
            'id' => false,
            'slug' => false,


            'e_commerce' => true,

            // 'status' => false,
            'date' => false,
            'time' => false,
        ]);
    }

    public $slug;
    public $websitting_id, $e_commerce;
    public $updateMode = false;

    private function resetInputFields()
    {
        $this->slug = '';

        $this->e_commerce = '';
    }


    public function rules()
    {
        return [
            // 'patient_id' => "required|unique:patient_studies,patient_id,$this->patientstudy_id,id,study_id,$this->study_id,deleted_at,NULL",
            // 'slug' => $this-slug,


            'e_commerce' => 'required',
        ];
    }

    public function updated($propertyName)
    {
        $this->validateOnly($propertyName);
    }

    public function store()
    {
        $validatedData = $this->validate();

        WebSitting::create([
            'slug' => $this->slug,

            'e_commerce' => $this->e_commerce,
        ]);

        session()->flash('message', 'WebSitting Created Successfully.');

        $this->resetInputFields();

        // $this->emit('close-model'); // Close model to using to jquery
    }

    public function edit($id)
    {
        $this->updateMode = true;
        $websitting = WebSitting::where('id', $id)->first();
        $this->websitting_id = $id;
        $this->slug = $websitting->slug;


        $this->e_commerce = $websitting->e_commerce;
    }

    public function cancel()
    {
        $this->updateMode = false;
        $this->resetInputFields();
    }

    public function update()
    {

        if ($this->websitting_id) {
            $websitting = WebSitting::find($this->websitting_id);
            $websitting->update([
                'slug' => $this->slug,

                'e_commerce' => $this->e_commerce,
            ]);

            $this->updateMode = false;
            session()->flash('message', 'WebSitting Updated Successfully.');
            $this->resetInputFields();
        }

        $this->emit('close-model'); // Close model to using to jquery
    }

    public function delete($id)
    {
        if ($id) {
            $websitting = WebSitting::find($id);

            $websitting->delete();

            session()->flash('message', 'WebSitting Deleted Successfully.');
        }
    }

    public function restore($id)
    {
        if ($id) {
            $websitting = WebSitting::withTrashed()->find($id);

            $websitting->restore();

            session()->flash('message', 'WebSitting Recovered Successfully.');
        }
    }

    public function updatedFromDate($fromDate)
    {
        $this->all = false;
    }

    public function updatedToDate($toDate)
    {
        $this->all = false;
    }

    public function clearFilter()
    {
        $this->all = true;
        // $this->fromDate = date('Y-m-d', strtotime("-5 days"));
        $this->byDate = 'created_at';
        $this->fromDate = '';
        $this->toDate = date('Y-m-d');
    }




    public function gotoPage($page)
    {
        $this->setPage($page);
        $this->emit('gotoTop');
    }

    public function nextPage()
    {
        $this->setPage($this->page + 1);
        $this->emit('gotoTop');
    }

    public function previousPage()
    {
        $this->setPage(max($this->page - 1, 1));
        $this->emit('gotoTop');
    }

    public function render()
    {
        $websittings = WebSitting::livewireSearch($this->search);

        if ($this->all == false)
            $websittings = $websittings->whereBetween($this->byDate, [$this->fromDate . ' 00:00:00', $this->toDate . ' 23:59:59']);




        if ($this->admin_view_status == 'deleted')
            $websittings = $websittings->onlyTrashed();


        $websittings = $websittings->orderBy($this->orderBy, $this->orderWay)
            ->paginate($this->perPage);

        return view('livewire.websitting.websitting-index', compact('websittings'));
    }
}
