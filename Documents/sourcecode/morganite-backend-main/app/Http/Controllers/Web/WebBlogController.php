<?php

namespace App\Http\Controllers\Web;

use App\Http\Controllers\Controller;
use App\Models\Blog;
use App\Models\Commint;
use Illuminate\Http\Request;

class WebBlogController extends Controller
{
    public function index()
    {
        $blogs = Blog::get();
        return view('web.blogs.blogs', compact('blogs'));
    }

    public function blogPage(Blog $blog)
    {
        $comments = Commint::where('the_model_name', 'blog')->where('the_model_id', $blog->id)->orderBy('id','desc')->paginate(3);

        $bolgs = Blog::inRandomOrder()->take(3)->get();

        return view('web.blogs.blog-page', compact('blog', 'comments', 'bolgs'));
    }

    public function postCommentBlog(Request $request)
    {
        $user_id = 0;
        $user_name = $request->input('name');
        $user_email = $request->input('email');

        if (auth()->check()) {
            $user_id = auth()->user()->id;
            $user_name = auth()->user()->crud_name();
            $user_email = auth()->user()->email;
        }

        $comments = Commint::create([
            'user_id' => $user_id,
            'name' => $user_name,
            'email' => $user_email,
            'content' => $request->input('comment'),
            'the_model_name' => 'blog',
            'the_model_id' => $request->input('blog_id', 0)
        ]);

        return redirect()->back();
    }
}
