<?php

namespace App\Http\Controllers;

use App\Models\User;
use Illuminate\Http\Request;

class UserController extends Controller
{
    public function index()
    {
        $type = 'all';
        return view('pages.user.user-home', compact('type'));
    }

    public function indexEmployee()
    {
        $type = 'employee';
        return view('pages.user.user-home', compact('type'));
    }

    public function indexCustomer()
    {
        $type = 'customer';
        return view('pages.user.user-home', compact('type'));
    }

    public function show(User $user)
    {
        return view('pages.user.user-show', compact('user'));
    }
}
