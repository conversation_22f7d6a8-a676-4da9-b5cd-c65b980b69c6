<?php

namespace App\Http\Controllers;

use App\Models\Blog;
use App\Models\Category;
use App\Models\Commint;
use App\Models\FirebaseNotification;
use App\Models\Item;
use App\Models\Order;
use App\Models\OrderStatus;
use App\Models\Product;
use App\Models\PromoCode;
use App\Models\Role;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Str;

class HomeController extends Controller
{
    public static function saveImageWeb($image, $folderName)
    {
        $theImageUrl_image = null;

        if (is_string($image)) return $image;

        if ($image) {
            $theImageName_image = $folderName . '_' . Str::random(10) . '.' . $image->extension();
            $image->storeAs($folderName, $theImageName_image);
            $theImageUrl_image = $folderName . '/' . $theImageName_image;

            // try {
            //     Image::load($theImageUrl_image)->quality(25)
            //         // ->width(300)->height(300)
            //         ->save(storage_path('app/' . $theImageUrl_image));
            // } catch (\Throwable $th) {
            // }
        }

        return $theImageUrl_image;
    }

    public function switchLang($lang)
    {
        if (array_key_exists($lang, Config::get('languages'))) {
            Session::put('applocale', $lang);
        }
        return Redirect::back();
    }

    public function sidenavToggled()
    {
        if (session('sidenav-toggled') == 'big') {
            session()->put('sidenav-toggled', 'small');
        } else {
            session()->put('sidenav-toggled', 'big');
        }

        return;
    }

    public function admin()
    {
        return redirect()->route('dashboard');
    }

    public function sound_notification(Request $request)
    {
        $user = $request->user();

        $count = FirebaseNotification::where('user_id', $user->id)
            ->where('seen', false)->count();

        return response()->json([
            "message" => "successfully get data",
            "data" => [
                'notification_count' => $count,
            ],
        ]);
    }

    public function see_notification(Request $request)
    {
        $user = $request->user();
        // $notification = FirebaseNotification::find($request->id);

        $notifications = FirebaseNotification::where('user_id', $user->id)
            ->where('seen', false)->get();

        foreach ($notifications as $notification) {
            $notification->update([
                'seen' => true,
                'seen_at' => date('Y-m-d H:i:s'),
            ]);
        }

        return response()->json([
            "message" => "successfully get data",
            "data" => [
                'notification' => 'done',
            ],
        ]);
    }

    public function home()
    {
        $listOfDates = [
            date('Y-m-d', strtotime("-7 days")),
            date('Y-m-d', strtotime("-6 days")),
            date('Y-m-d', strtotime("-5 days")),
            date('Y-m-d', strtotime("-4 days")),
            date('Y-m-d', strtotime("-3 days")),
            date('Y-m-d', strtotime("-2 days")),
            date('Y-m-d', strtotime("-1 days")),
            date('Y-m-d', strtotime("0 days")),
        ];

        $userList = [];
        $blogsCom = [];

        for ($i = 0; $i < count($listOfDates); $i++) {
            $userList[] = User::where('created_at', 'like', '%' . $listOfDates[$i] . '%')->count();
            $blogsCom[] = Commint::where('created_at', 'like', '%' . $listOfDates[$i] . '%')->count();
        }


        $customer           = User::whereRoleIs('customer')->count();;
        $products           = Product::count();
        $blogs              = Blog::count();
        $promocodeAvilabel  = PromoCode::where('status', 'active')->count();
        $promocodePinding   = PromoCode::where('status', 'pending')->count();
        $categories         = Category::count();

        $mostItemPopular    = Item::groupBy('product_id')->selectRaw('*, sum(quantity) as quantity')->with('product')->orderByDesc('quantity')->limit(5)->get();




        // dd($mostItemPopular);

        $listOfDates[] = date('Y-m-d', strtotime("+1 days"));
        $listOfDates[] = date('Y-m-d', strtotime("+2 days"));



        return view('home', compact(
            'listOfDates',
            'userList',
            'customer',
            'products',
            'promocodeAvilabel',
            'promocodePinding',
            'blogs',
            'categories',
            'mostItemPopular',
            'blogsCom'
        ));
    }

    public function editProfile()
    {
        $user = Auth::user();

        return view('stander.user.edit-profile', compact('user'));
    }

    public function updatePassword(Request $request)
    {
        $request->validate([
            // 'current_password' => 'required',
            'new_password' => 'required',
            'confirm_password' => 'required',
        ]);

        // $current_password = $request->input('current_password');
        $new_password = $request->input('new_password');
        $confirm_password = $request->input('confirm_password');

        $user = $request->user();

        // if (!Hash::check($current_password, $user->password))
        //     return back()->withInput()->with('error', __('global.Current Password Not Correct'));

        if ($new_password != $confirm_password)
            return back()->withInput()->with('error', __('global.Password Not Match'));


        $user->update(['password' => Hash::make($new_password)]);

        return back()->with('success', __('global.Password Changed'));
    }

    public function updateProfile(Request $request)
    {
        $user = $request->user();

        $request->validate([
            'first_name' => 'required',
            'last_name' => 'required',
            'user_name' => 'required',
            'email' => 'required',
            'phone' => 'required',
            'gender' => 'required',
            'birth_day' => 'required',
        ]);

        $user->update([
            'first_name' => $request->input('first_name', $user->first_name),
            'last_name' => $request->input('last_name', $user->last_name),
            'user_name' => $request->input('user_name', $user->user_name),
            'email' => $request->input('email', $user->email),
            'phone' => $request->input('phone', $user->phone),
            'gender' => $request->input('gender', $user->gender),
            'birth_day' => $request->input('birth_day', $user->birth_day),
        ]);


        return back()->with('success', __('global.Profile Updated'));
    }
}
