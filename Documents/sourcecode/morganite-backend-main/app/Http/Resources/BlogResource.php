<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class BlogResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        // return parent::toArray($request);
        return [
            "blog_id" => $this->id,
            "slug" => $this->slug,


            'the_title' => $this->the_title,
            'the_content' => $this->the_content,
            'thumbnail' => $this->thumbnail,
            'auther' => new UserResource($this->whenLoaded('auther')),
            'the_summary' => $this->the_summary,

            "date" => date("d/m/Y", strtotime($this->created_at)),
            "time" => date("h:i A", strtotime($this->created_at)),
            "date_time" => date("Y-m-d h:i A", strtotime($this->created_at)),
            "created_ago" => $this->created_ago($this->created_at, $request->header("lang")),
        ];
    }
}
