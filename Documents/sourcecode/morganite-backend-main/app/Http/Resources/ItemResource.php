<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class ItemResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        // return parent::toArray($request);
        return [
            "item_id" => $this->id,
            "slug" => $this->slug,

            
                    'order' => new OrderResource($this->whenLoaded('order')),
                    'product' => new ProductResource($this->whenLoaded('product')),
                        'price' => $this->price,
                        'quantity' => $this->quantity,
                    'attribute_value' => new Attribute_valueResource($this->whenLoaded('attribute_value')),
                        'note' => $this->note,

            "date" => date("d/m/Y", strtotime($this->created_at)),
            "time" => date("h:i A", strtotime($this->created_at)),
            "date_time" => date("Y-m-d h:i A", strtotime($this->created_at)),
            "created_ago" => $this->created_ago($this->created_at, $request->header("lang")),
        ];
    }
}
