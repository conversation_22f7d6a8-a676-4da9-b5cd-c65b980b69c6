<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class ProductResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        // return parent::toArray($request);
        return [
            "product_id" => $this->id,
            "slug" => $this->slug,


            'sku' => $this->sku,
            'the_name' => $this->the_name,
            'the_short_description' => $this->the_short_description,
            'the_long_description' => $this->the_long_description,
            'status' => $this->status,
            'thumbnail' => $this->thumbnail ? asset($this->thumbnail) : null,

            "date" => date("d/m/Y", strtotime($this->created_at)),
            "time" => date("h:i A", strtotime($this->created_at)),
            "date_time" => date("Y-m-d h:i A", strtotime($this->created_at)),
            "created_ago" => $this->created_ago($this->created_at, $request->header("lang")),
        ];
    }
}
