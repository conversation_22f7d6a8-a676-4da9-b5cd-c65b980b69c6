<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class BannerResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        // return parent::toArray($request);
        return [
            "banner_id" => $this->id,
            "slug" => $this->slug,


            'image' => $this->image ? asset($this->image) : null,
            'the_title' => $this->the_title,
            'the_description' => $this->the_description,
            'the_model' => $this->the_model,
            'the_model_id' => $this->the_model_id,
            'place' => $this->place,
            'status' => $this->status,
            'from_date' => $this->from_date,
            'to_date' => $this->to_date,

            "date" => date("d/m/Y", strtotime($this->created_at)),
            "time" => date("h:i A", strtotime($this->created_at)),
            "date_time" => date("Y-m-d h:i A", strtotime($this->created_at)),
            "created_ago" => $this->created_ago($this->created_at, $request->header("lang")),
        ];
    }
}
