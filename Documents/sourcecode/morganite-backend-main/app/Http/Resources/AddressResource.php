<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class AddressResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        // return parent::toArray($request);
        return [
            "address_id" => $this->id,
            "slug" => $this->slug,

            
                    'user' => new UserResource($this->whenLoaded('user')),
                        'nick_name' => $this->nick_name,
                        'location' => $this->location,
                        'latitude' => $this->latitude,
                        'longitude' => $this->longitude,
                    'country' => new CountryResource($this->whenLoaded('country')),
                    'city' => new CityResource($this->whenLoaded('city')),
                    'area' => new AreaResource($this->whenLoaded('area')),
                        'note' => $this->note,

            "date" => date("d/m/Y", strtotime($this->created_at)),
            "time" => date("h:i A", strtotime($this->created_at)),
            "date_time" => date("Y-m-d h:i A", strtotime($this->created_at)),
            "created_ago" => $this->created_ago($this->created_at, $request->header("lang")),
        ];
    }
}
