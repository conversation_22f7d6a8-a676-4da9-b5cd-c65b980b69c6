<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class CommintResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        // return parent::toArray($request);
        return [
            "commint_id" => $this->id,
            "slug" => $this->slug,

            
                    'user' => new UserResource($this->whenLoaded('user')),
                        'name' => $this->name,
                        'email' => $this->email,
                        'content' => $this->content,
                        'the_model_name' => $this->the_model_name,
                        'the_model_id' => $this->the_model_id,

            "date" => date("d/m/Y", strtotime($this->created_at)),
            "time" => date("h:i A", strtotime($this->created_at)),
            "date_time" => date("Y-m-d h:i A", strtotime($this->created_at)),
            "created_ago" => $this->created_ago($this->created_at, $request->header("lang")),
        ];
    }
}
