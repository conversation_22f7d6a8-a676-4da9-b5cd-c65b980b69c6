<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class PromoCodeResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        // return parent::toArray($request);
        return [
            "promocode_id" => $this->id,
            "slug" => $this->slug,

            
                        'the_name' => $this->the_name,
                        'code' => $this->code,
                        'discount_type' => $this->discount_type,
                        'discount_amount' => $this->discount_amount,
                        'type' => $this->type,
                        'max_allowed_discount' => $this->max_allowed_discount,
                        'used_count' => $this->used_count,
                        'active_from_date' => $this->active_from_date,
                        'active_to_date' => $this->active_to_date,
                        'status' => $this->status,

            "date" => date("d/m/Y", strtotime($this->created_at)),
            "time" => date("h:i A", strtotime($this->created_at)),
            "date_time" => date("Y-m-d h:i A", strtotime($this->created_at)),
            "created_ago" => $this->created_ago($this->created_at, $request->header("lang")),
        ];
    }
}
