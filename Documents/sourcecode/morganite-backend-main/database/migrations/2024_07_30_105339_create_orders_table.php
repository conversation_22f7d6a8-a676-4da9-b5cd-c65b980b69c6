<?php

use App\Models\User;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateOrdersTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('orders', function (Blueprint $table) {
            $table->bigIncrements('id');

            $table->foreignIdFor(User::class, 'add_by')->default(0);
            $table->string('slug')->nullable();


            $table->foreignIdFor(App\Models\User::class)->nullable();

            $table->foreignIdFor(App\Models\OrderStatus::class)->nullable();

            $table->double('total_price')->nullable();

            $table->date('delivery_date')->nullable();

            $table->string('delivery_status')->nullable();

            $table->double('delivery_fee')->nullable();

            $table->string('payment_method')->nullable();

            $table->string('payment_status')->nullable();

            $table->foreignIdFor(App\Models\Address::class)->nullable();

            $table->foreignIdFor(App\Models\PromoCode::class)->nullable();

            $table->string('discount_type')->nullable();

            $table->double('discount_amount')->nullable();

            $table->double('final_price')->nullable();


            $table->boolean('show')->default(true);
            $table->integer('sort')->default(1000);
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('orders');
    }
}
