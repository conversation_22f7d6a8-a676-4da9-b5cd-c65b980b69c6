<?php

use App\Models\User;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateAddressesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('addresses', function (Blueprint $table) {
            $table->bigIncrements('id');

            $table->foreignIdFor(User::class, 'add_by')->default(0);
            $table->string('slug')->nullable();


            $table->foreignIdFor(App\Models\User::class)->nullable();

            $table->string('nick_name')->nullable();

            $table->string('location')->nullable();

            $table->string('latitude')->nullable();

            $table->string('longitude')->nullable();

            $table->foreignIdFor(App\Models\Country::class)->nullable();

            $table->foreignIdFor(App\Models\City::class)->nullable();

            $table->foreignIdFor(App\Models\Area::class)->nullable();

            $table->string('note')->nullable();


            $table->boolean('show')->default(true);
            $table->integer('sort')->default(1000);
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('addresses');
    }
}
