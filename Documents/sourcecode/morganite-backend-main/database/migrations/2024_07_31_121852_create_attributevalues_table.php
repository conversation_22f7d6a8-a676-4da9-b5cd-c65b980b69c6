<?php

use App\Models\User;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateAttributeValuesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('attribute_values', function (Blueprint $table) {
            $table->bigIncrements('id');

            $table->foreignIdFor(User::class, 'add_by')->default(0);
            $table->string('slug')->nullable();


            $table->string('sku')->nullable();

            $table->json('the_name')->nullable();

            $table->json('the_description')->nullable();

            $table->double('price')->nullable();

            $table->string('discount_type')->nullable();

            $table->double('discount_amount')->nullable();

            $table->string('manage_stock')->nullable();

            $table->integer('quantity_in_stock')->nullable();

            $table->date('discount_from_date')->nullable();

            $table->date('discount_to_date')->nullable();

            $table->string('status')->nullable();

            $table->foreignIdFor(App\Models\AttributeGroup::class)->nullable();

            $table->foreignIdFor(App\Models\Product::class)->nullable();


            $table->boolean('show')->default(true);
            $table->integer('sort')->default(1000);
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('attribute_values');
    }
}
