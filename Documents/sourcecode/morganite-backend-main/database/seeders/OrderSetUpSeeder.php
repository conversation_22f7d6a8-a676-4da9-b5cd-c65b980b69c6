<?php

namespace Database\Seeders;

use App\Models\Applang;
use App\Models\Apptext;
use App\Models\OrderStatus;
use Illuminate\Database\Seeder;

class OrderSetUpSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $statuses = [
            [
                'slug' => 'on-cart',
                'name' => [
                    'ar' => 'في السلة',
                    'en' => 'on cart',
                ],
                'color' => '#00ff00',
            ],
            [
                'slug' => 'pending',
                'name' => [
                    'ar' => 'قيد الانتظار',
                    'en' => 'pending',
                ],
                'color' => '#FFF67E',
            ],
            [
                'slug' => 'accepted',
                'name' => [
                    'ar' => 'تم القبول',
                    'en' => 'accepted',
                ],
                'color' => '#BFEA7C',
            ],
            [
                'slug' => 'rejected',
                'name' => [
                    'ar' => 'مرفوض',
                    'en' => 'rejected',
                ],
                'color' => '#B31312',
            ],
            [
                'slug' => 'preparing',
                'name' => [
                    'ar' => 'قيد التحضير',
                    'en' => 'preparing',
                ],
                'color' => '#9BCF53',
            ],
            [
                'slug' => 'on-delivery',
                'name' => [
                    'ar' => 'قيد التوصيل',
                    'en' => 'on delivery',
                ],
                'color' => '#FFC700',
            ],
            [
                'slug' => 'cancel',
                'name' => [
                    'ar' => 'ملغي',
                    'en' => 'canceled',
                ],
                'color' => '#B31312',
            ],
            [
                'slug' => 'completed',
                'name' => [
                    'ar' => 'مكتمل',
                    'en' => 'completed',
                ],
                'color' => '#4CBB17',
            ],
        ];

        foreach ($statuses as $value) {
            $status = OrderStatus::where('slug', 'status-' . $value['slug'])->first();

            if (!$status) {
                $status = OrderStatus::create([
                    'slug' => 'status-' . $value['slug'],
                    'the_name' => $value['name'],
                    // 'bg_color' => $value['name'],
                    'text_color' => $value['color'],
                ]);

                // foreach ($value['name'] as $key => $val) {
                //     Apptext::create([
                //         'applang_id' => Applang::where('code', $key)->first()->id,
                //         'text' => $val,
                //         'the_model_name' => 'OrderStatus',
                //         'the_model_id' => $status->id,
                //         'place' => 'name',
                //     ]);
                // }
            }
        }


        // // // // // // // // // 
        // // // // // // // // // 
    }
}
